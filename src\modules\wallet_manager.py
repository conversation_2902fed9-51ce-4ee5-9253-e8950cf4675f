#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo de gerenciamento de carteiras/contas
"""

from datetime import datetime
import sqlite3

class WalletManager:
    def __init__(self, db_manager):
        self.db_manager = db_manager
    
    def create_wallet(self, user_id, name, description="", initial_balance=0.0, wallet_type="checking"):
        """Cria uma nova carteira"""
        try:
            # Validações
            if not name or len(name.strip()) == 0:
                raise ValueError("Nome da carteira é obrigatório")
            
            if len(name) > 100:
                raise ValueError("Nome da carteira deve ter no máximo 100 caracteres")
            
            if initial_balance < 0:
                raise ValueError("Saldo inicial não pode ser negativo")
            
            valid_types = ['checking', 'savings', 'credit', 'cash']
            if wallet_type not in valid_types:
                raise ValueError(f"Tipo de carteira inválido. Use: {', '.join(valid_types)}")
            
            # Verificar se já existe carteira com mesmo nome para o usuário
            if self.wallet_name_exists(user_id, name):
                raise ValueError("Já existe uma carteira com este nome")
            
            # Inserir carteira
            query = '''
                INSERT INTO wallets (user_id, name, description, initial_balance, 
                                   current_balance, wallet_type)
                VALUES (?, ?, ?, ?, ?, ?)
            '''
            
            wallet_id = self.db_manager.execute_query_with_id(query, (
                user_id, name.strip(), description.strip(),
                initial_balance, initial_balance, wallet_type
            ))

            return wallet_id
            
        except Exception as e:
            raise Exception(f"Erro ao criar carteira: {str(e)}")
    
    def wallet_name_exists(self, user_id, name, exclude_id=None):
        """Verifica se já existe carteira com o mesmo nome"""
        query = "SELECT COUNT(*) as count FROM wallets WHERE user_id = ? AND name = ?"
        params = [user_id, name.strip()]

        if exclude_id:
            query += " AND id != ?"
            params.append(exclude_id)

        result = self.db_manager.execute_query(query, params)
        return result[0]['count'] > 0
    
    def get_user_wallets(self, user_id, include_inactive=False):
        """Retorna todas as carteiras do usuário"""
        query = '''
            SELECT id, name, description, initial_balance, current_balance, 
                   wallet_type, is_active, created_at
            FROM wallets
            WHERE user_id = ?
        '''
        
        if not include_inactive:
            query += " AND is_active = TRUE"
        
        query += " ORDER BY name"
        
        return self.db_manager.execute_query(query, (user_id,))
    
    def get_wallet_by_id(self, wallet_id, user_id):
        """Retorna carteira específica"""
        query = '''
            SELECT id, name, description, initial_balance, current_balance, 
                   wallet_type, is_active, created_at
            FROM wallets
            WHERE id = ? AND user_id = ?
        '''
        
        result = self.db_manager.execute_query(query, (wallet_id, user_id))
        return result[0] if result else None
    
    def update_wallet(self, wallet_id, user_id, name=None, description=None, 
                     initial_balance=None, wallet_type=None):
        """Atualiza dados da carteira"""
        try:
            # Verificar se carteira existe e pertence ao usuário
            wallet = self.get_wallet_by_id(wallet_id, user_id)
            if not wallet:
                raise ValueError("Carteira não encontrada")
            
            updates = []
            params = []
            
            # Validar e preparar atualizações
            if name is not None:
                if not name or len(name.strip()) == 0:
                    raise ValueError("Nome da carteira é obrigatório")
                if len(name) > 100:
                    raise ValueError("Nome da carteira deve ter no máximo 100 caracteres")
                if self.wallet_name_exists(user_id, name, wallet_id):
                    raise ValueError("Já existe uma carteira com este nome")
                
                updates.append("name = ?")
                params.append(name.strip())
            
            if description is not None:
                updates.append("description = ?")
                params.append(description.strip())
            
            if initial_balance is not None:
                if initial_balance < 0:
                    raise ValueError("Saldo inicial não pode ser negativo")
                
                # Calcular diferença para ajustar saldo atual
                old_initial = wallet['initial_balance']
                difference = initial_balance - old_initial
                new_current = wallet['current_balance'] + difference
                
                updates.append("initial_balance = ?")
                updates.append("current_balance = ?")
                params.extend([initial_balance, new_current])
            
            if wallet_type is not None:
                valid_types = ['checking', 'savings', 'credit', 'cash']
                if wallet_type not in valid_types:
                    raise ValueError(f"Tipo de carteira inválido. Use: {', '.join(valid_types)}")
                
                updates.append("wallet_type = ?")
                params.append(wallet_type)
            
            if not updates:
                return True  # Nada para atualizar
            
            # Executar atualização
            query = f"UPDATE wallets SET {', '.join(updates)} WHERE id = ? AND user_id = ?"
            params.extend([wallet_id, user_id])
            
            self.db_manager.execute_query(query, params)
            return True
            
        except Exception as e:
            raise Exception(f"Erro ao atualizar carteira: {str(e)}")
    
    def toggle_wallet_status(self, wallet_id, user_id):
        """Ativa/desativa carteira"""
        try:
            wallet = self.get_wallet_by_id(wallet_id, user_id)
            if not wallet:
                raise ValueError("Carteira não encontrada")
            
            query = "UPDATE wallets SET is_active = NOT is_active WHERE id = ? AND user_id = ?"
            self.db_manager.execute_query(query, (wallet_id, user_id))
            
            return True
            
        except Exception as e:
            raise Exception(f"Erro ao alterar status da carteira: {str(e)}")
    
    def delete_wallet(self, wallet_id, user_id):
        """Exclui carteira (apenas se não tiver transações)"""
        try:
            wallet = self.get_wallet_by_id(wallet_id, user_id)
            if not wallet:
                raise ValueError("Carteira não encontrada")
            
            # Verificar se há transações associadas
            query = "SELECT COUNT(*) as count FROM transactions WHERE wallet_id = ?"
            result = self.db_manager.execute_query(query, (wallet_id,))

            if result[0]['count'] > 0:
                raise ValueError("Não é possível excluir carteira com transações. Desative-a em vez disso.")
            
            # Excluir carteira
            query = "DELETE FROM wallets WHERE id = ? AND user_id = ?"
            self.db_manager.execute_query(query, (wallet_id, user_id))
            
            return True
            
        except Exception as e:
            raise Exception(f"Erro ao excluir carteira: {str(e)}")
    
    def update_wallet_balance(self, wallet_id, amount, operation='add'):
        """Atualiza saldo da carteira"""
        try:
            if operation not in ['add', 'subtract']:
                raise ValueError("Operação deve ser 'add' ou 'subtract'")
            
            if operation == 'add':
                query = "UPDATE wallets SET current_balance = current_balance + ? WHERE id = ?"
            else:
                query = "UPDATE wallets SET current_balance = current_balance - ? WHERE id = ?"
            
            self.db_manager.execute_query(query, (abs(amount), wallet_id))
            return True
            
        except Exception as e:
            raise Exception(f"Erro ao atualizar saldo da carteira: {str(e)}")
    
    def get_wallet_summary(self, user_id):
        """Retorna resumo das carteiras do usuário"""
        try:
            query = '''
                SELECT 
                    COUNT(*) as total_wallets,
                    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_wallets,
                    COALESCE(SUM(CASE WHEN is_active = TRUE THEN current_balance ELSE 0 END), 0) as total_balance,
                    COALESCE(SUM(CASE WHEN is_active = TRUE THEN initial_balance ELSE 0 END), 0) as total_initial
                FROM wallets
                WHERE user_id = ?
            '''
            
            result = self.db_manager.execute_query(query, (user_id,))
            
            if result:
                summary = dict(result[0])
                summary['balance_difference'] = summary['total_balance'] - summary['total_initial']
                return summary
            
            return {
                'total_wallets': 0,
                'active_wallets': 0,
                'total_balance': 0.0,
                'total_initial': 0.0,
                'balance_difference': 0.0
            }
            
        except Exception as e:
            raise Exception(f"Erro ao obter resumo das carteiras: {str(e)}")
    
    def get_wallet_types_summary(self, user_id):
        """Retorna resumo por tipo de carteira"""
        try:
            query = '''
                SELECT 
                    wallet_type,
                    COUNT(*) as count,
                    COALESCE(SUM(current_balance), 0) as total_balance
                FROM wallets
                WHERE user_id = ? AND is_active = TRUE
                GROUP BY wallet_type
                ORDER BY total_balance DESC
            '''
            
            return self.db_manager.execute_query(query, (user_id,))
            
        except Exception as e:
            raise Exception(f"Erro ao obter resumo por tipo: {str(e)}")
