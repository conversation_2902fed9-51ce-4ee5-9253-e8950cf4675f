#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Exemplo completo de como bloquear redimensionamento de formulários
"""

import tkinter as tk
from tkinter import ttk, messagebox

class ExemploRedimensionamento:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Exemplos de Bloqueio de Redimensionamento")
        self.root.geometry("600x400")
        
        self.create_interface()
    
    def create_interface(self):
        """Cria interface principal"""
        # Frame principal
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Título
        title = tk.Label(
            main_frame,
            text="🔒 EXEMPLOS DE BLOQUEIO DE REDIMENSIONAMENTO",
            font=("Arial", 16, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title.pack(pady=(0, 30))
        
        # Botões de exemplo
        self.create_buttons(main_frame)
        
        # Informações
        self.create_info(main_frame)
    
    def create_buttons(self, parent):
        """Cria botões de exemplo"""
        buttons_frame = tk.Frame(parent, bg='#f0f0f0')
        buttons_frame.pack(pady=(0, 20))
        
        # Botão 1: Bloqueio total
        btn1 = tk.Button(
            buttons_frame,
            text="🚫 Formulário Totalmente Bloqueado",
            command=self.exemplo_bloqueio_total,
            font=("Arial", 11, "bold"),
            bg='#e74c3c',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        btn1.pack(pady=(0, 10))
        
        # Botão 2: Bloqueio só horizontal
        btn2 = tk.Button(
            buttons_frame,
            text="↔️ Bloqueio Só Horizontal",
            command=self.exemplo_bloqueio_horizontal,
            font=("Arial", 11, "bold"),
            bg='#f39c12',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        btn2.pack(pady=(0, 10))
        
        # Botão 3: Bloqueio só vertical
        btn3 = tk.Button(
            buttons_frame,
            text="↕️ Bloqueio Só Vertical",
            command=self.exemplo_bloqueio_vertical,
            font=("Arial", 11, "bold"),
            bg='#3498db',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        btn3.pack(pady=(0, 10))
        
        # Botão 4: Tamanho fixo com minsize/maxsize
        btn4 = tk.Button(
            buttons_frame,
            text="📏 Tamanho Fixo (minsize/maxsize)",
            command=self.exemplo_tamanho_fixo,
            font=("Arial", 11, "bold"),
            bg='#27ae60',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        btn4.pack()
    
    def create_info(self, parent):
        """Cria área de informações"""
        info_frame = tk.LabelFrame(
            parent,
            text="📋 Métodos de Bloqueio",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        info_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        info_text = tk.Text(
            info_frame,
            height=12,
            font=("Consolas", 10),
            bg='#ffffff',
            fg='#2c3e50',
            relief=tk.SOLID,
            bd=1,
            wrap=tk.WORD
        )
        info_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Informações sobre os métodos
        info_content = """
🔒 MÉTODOS PARA BLOQUEAR REDIMENSIONAMENTO:

1. 🚫 BLOQUEIO TOTAL:
   window.resizable(False, False)
   • Bloqueia largura E altura
   • Usuário não pode redimensionar nada

2. ↔️ BLOQUEIO HORIZONTAL:
   window.resizable(False, True)
   • Bloqueia largura
   • Permite alterar altura

3. ↕️ BLOQUEIO VERTICAL:
   window.resizable(True, False)
   • Permite alterar largura
   • Bloqueia altura

4. 📏 TAMANHO FIXO:
   window.minsize(400, 300)
   window.maxsize(400, 300)
   • Define tamanho mínimo e máximo iguais
   • Resultado: tamanho fixo

5. 🎯 APLICAÇÃO NO SEU CÓDIGO:
   # Após criar a janela:
   user_window = tk.Toplevel(parent)
   user_window.geometry("400x500")
   user_window.resizable(False, False)  # ← ADICIONE ESTA LINHA
"""
        
        info_text.insert('1.0', info_content)
        info_text.config(state='disabled')  # Tornar somente leitura
    
    def exemplo_bloqueio_total(self):
        """Exemplo de bloqueio total"""
        window = tk.Toplevel(self.root)
        window.title("🚫 Bloqueio Total")
        window.geometry("300x200")
        window.resizable(False, False)  # BLOQUEIO TOTAL
        window.transient(self.root)
        window.grab_set()
        
        # Conteúdo
        frame = tk.Frame(window, bg='#e74c3c')
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        tk.Label(
            frame,
            text="🚫 REDIMENSIONAMENTO\nTOTALMENTE BLOQUEADO",
            font=("Arial", 12, "bold"),
            bg='#e74c3c',
            fg='white',
            justify=tk.CENTER
        ).pack(expand=True)
        
        tk.Button(
            frame,
            text="Fechar",
            command=window.destroy,
            bg='white',
            fg='#e74c3c',
            font=("Arial", 10, "bold")
        ).pack(pady=(10, 0))
    
    def exemplo_bloqueio_horizontal(self):
        """Exemplo de bloqueio horizontal"""
        window = tk.Toplevel(self.root)
        window.title("↔️ Bloqueio Horizontal")
        window.geometry("300x200")
        window.resizable(False, True)  # Bloqueia largura, permite altura
        window.transient(self.root)
        window.grab_set()
        
        # Conteúdo
        frame = tk.Frame(window, bg='#f39c12')
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        tk.Label(
            frame,
            text="↔️ LARGURA BLOQUEADA\n↕️ Altura pode mudar",
            font=("Arial", 12, "bold"),
            bg='#f39c12',
            fg='white',
            justify=tk.CENTER
        ).pack(expand=True)
        
        tk.Button(
            frame,
            text="Fechar",
            command=window.destroy,
            bg='white',
            fg='#f39c12',
            font=("Arial", 10, "bold")
        ).pack(pady=(10, 0))
    
    def exemplo_bloqueio_vertical(self):
        """Exemplo de bloqueio vertical"""
        window = tk.Toplevel(self.root)
        window.title("↕️ Bloqueio Vertical")
        window.geometry("300x200")
        window.resizable(True, False)  # Permite largura, bloqueia altura
        window.transient(self.root)
        window.grab_set()
        
        # Conteúdo
        frame = tk.Frame(window, bg='#3498db')
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        tk.Label(
            frame,
            text="↔️ Largura pode mudar\n↕️ ALTURA BLOQUEADA",
            font=("Arial", 12, "bold"),
            bg='#3498db',
            fg='white',
            justify=tk.CENTER
        ).pack(expand=True)
        
        tk.Button(
            frame,
            text="Fechar",
            command=window.destroy,
            bg='white',
            fg='#3498db',
            font=("Arial", 10, "bold")
        ).pack(pady=(10, 0))
    
    def exemplo_tamanho_fixo(self):
        """Exemplo de tamanho fixo com minsize/maxsize"""
        window = tk.Toplevel(self.root)
        window.title("📏 Tamanho Fixo")
        window.geometry("300x200")
        window.minsize(300, 200)  # Tamanho mínimo
        window.maxsize(300, 200)  # Tamanho máximo = mínimo = fixo
        window.transient(self.root)
        window.grab_set()
        
        # Conteúdo
        frame = tk.Frame(window, bg='#27ae60')
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        tk.Label(
            frame,
            text="📏 TAMANHO FIXO\n(minsize = maxsize)",
            font=("Arial", 12, "bold"),
            bg='#27ae60',
            fg='white',
            justify=tk.CENTER
        ).pack(expand=True)
        
        tk.Button(
            frame,
            text="Fechar",
            command=window.destroy,
            bg='white',
            fg='#27ae60',
            font=("Arial", 10, "bold")
        ).pack(pady=(10, 0))
    
    def run(self):
        """Executa o exemplo"""
        self.root.mainloop()

def main():
    """Função principal"""
    print("🔒 EXEMPLO DE BLOQUEIO DE REDIMENSIONAMENTO")
    print("=" * 50)
    print("Teste os diferentes tipos de bloqueio!")
    print()
    
    app = ExemploRedimensionamento()
    app.run()

if __name__ == "__main__":
    main()
