#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo de gerenciamento do banco de dados SQLite
"""

import sqlite3
import os
from datetime import datetime, date, timedelta
import json
from pathlib import Path

class DatabaseManager:
    def __init__(self, db_path="data/gestao_contas.db"):
        self.db_path = db_path
        self.ensure_data_directory()
    
    def ensure_data_directory(self):
        """Garante que o diretório de dados existe"""
        data_dir = Path(self.db_path).parent
        data_dir.mkdir(exist_ok=True)
    
    def get_connection(self):
        """Retorna uma conexão com o banco de dados"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Para acessar colunas por nome
        return conn
    
    def initialize_database(self):
        """Inicializa o banco de dados com todas as tabelas necessárias"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Tabela de usuários
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    full_name TEXT NOT NULL,
                    is_admin BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            ''')
            
            # Tabela de carteiras/contas
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS wallets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    initial_balance DECIMAL(10,2) DEFAULT 0.00,
                    current_balance DECIMAL(10,2) DEFAULT 0.00,
                    wallet_type TEXT DEFAULT 'checking', -- checking, savings, credit, cash
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Tabela de categorias
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER, -- NULL for global categories, user_id for user-specific
                    name TEXT NOT NULL,
                    description TEXT,
                    category_type TEXT NOT NULL, -- income, expense
                    color TEXT DEFAULT '#007ACC',
                    icon TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Tabela de transações (receitas e despesas)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    wallet_id INTEGER NOT NULL,
                    category_id INTEGER NOT NULL,
                    transaction_type TEXT NOT NULL, -- income, expense
                    amount DECIMAL(10,2) NOT NULL,
                    description TEXT NOT NULL,
                    transaction_date DATE NOT NULL,
                    due_date DATE,
                    is_paid BOOLEAN DEFAULT FALSE,
                    is_recurring BOOLEAN DEFAULT FALSE,
                    recurring_type TEXT, -- monthly, weekly, yearly
                    recurring_day INTEGER,
                    notes TEXT,
                    installments INTEGER DEFAULT 1,
                    installment_number INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (wallet_id) REFERENCES wallets (id),
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
            ''')
            
            # Tabela de configurações do sistema
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    setting_key TEXT NOT NULL,
                    setting_value TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Tabela de logs do sistema
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    description TEXT,
                    ip_address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # Tabela de veículos
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS vehicles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    brand TEXT NOT NULL,
                    model TEXT NOT NULL,
                    year INTEGER NOT NULL,
                    license_plate TEXT UNIQUE,
                    color TEXT,
                    fuel_type TEXT DEFAULT 'gasoline', -- gasoline, ethanol, diesel, flex, electric
                    engine_size TEXT,
                    mileage INTEGER DEFAULT 0,
                    purchase_date DATE,
                    purchase_price DECIMAL(10,2),
                    current_value DECIMAL(10,2),
                    insurance_company TEXT,
                    insurance_policy TEXT,
                    insurance_expiry DATE,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # Tabela de manutenção de veículos
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS vehicle_maintenance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    vehicle_id INTEGER NOT NULL,
                    maintenance_type TEXT NOT NULL, -- oil_change, tire_change, brake_service, general_service, repair
                    description TEXT NOT NULL,
                    service_date DATE NOT NULL,
                    mileage_at_service INTEGER,
                    cost DECIMAL(10,2) NOT NULL,
                    service_provider TEXT,
                    next_service_date DATE,
                    next_service_mileage INTEGER,
                    warranty_period INTEGER, -- em meses
                    warranty_expiry DATE,
                    receipt_number TEXT,
                    notes TEXT,
                    is_scheduled BOOLEAN DEFAULT FALSE,
                    is_completed BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (vehicle_id) REFERENCES vehicles (id) ON DELETE CASCADE
                )
            ''')

            # Tabela de abastecimento de combustível
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS fuel_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    vehicle_id INTEGER NOT NULL,
                    fuel_date DATE NOT NULL,
                    fuel_type TEXT NOT NULL, -- gasoline, ethanol, diesel
                    liters DECIMAL(8,3) NOT NULL,
                    price_per_liter DECIMAL(6,3) NOT NULL,
                    total_cost DECIMAL(10,2) NOT NULL,
                    mileage INTEGER,
                    gas_station TEXT,
                    location TEXT,
                    is_full_tank BOOLEAN DEFAULT TRUE,
                    fuel_efficiency DECIMAL(6,2), -- km/l calculado automaticamente
                    notes TEXT,
                    receipt_number TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (vehicle_id) REFERENCES vehicles (id) ON DELETE CASCADE
                )
            ''')

            # Criar categorias padrão se não existirem
            self.create_default_categories(cursor)
            
            conn.commit()
            print("Banco de dados inicializado com sucesso!")
            
        except Exception as e:
            conn.rollback()
            raise Exception(f"Erro ao inicializar banco de dados: {str(e)}")
        finally:
            conn.close()
    
    def create_default_categories(self, cursor):
        """Cria categorias padrão para novos usuários"""
        # Verificar se já existem categorias padrão
        try:
            cursor.execute("SELECT COUNT(*) FROM categories WHERE user_id IS NULL")
            if cursor.fetchone()[0] > 0:
                return
        except:
            # Se der erro, continua para criar as categorias
            pass

        # Categorias de receita padrão
        income_categories = [
            ('Salário', 'Salário mensal', '#28a745'),
            ('Freelance', 'Trabalhos extras', '#17a2b8'),
            ('Investimentos', 'Rendimentos de investimentos', '#ffc107'),
            ('Outros', 'Outras receitas', '#6c757d')
        ]
        
        # Categorias de despesa padrão
        expense_categories = [
            ('Alimentação', 'Gastos com comida', '#dc3545'),
            ('Transporte', 'Gastos com transporte', '#fd7e14'),
            ('Moradia', 'Aluguel, condomínio, etc.', '#6f42c1'),
            ('Saúde', 'Gastos médicos', '#e83e8c'),
            ('Educação', 'Cursos, livros, etc.', '#20c997'),
            ('Lazer', 'Entretenimento', '#0dcaf0'),
            ('Outros', 'Outras despesas', '#6c757d')
        ]
        
        # Inserir categorias de receita
        for name, desc, color in income_categories:
            cursor.execute('''
                INSERT OR IGNORE INTO categories (user_id, name, description, category_type, color)
                VALUES (NULL, ?, ?, 'income', ?)
            ''', (name, desc, color))

        # Inserir categorias de despesa
        for name, desc, color in expense_categories:
            cursor.execute('''
                INSERT OR IGNORE INTO categories (user_id, name, description, category_type, color)
                VALUES (NULL, ?, ?, 'expense', ?)
            ''', (name, desc, color))
    
    def execute_query(self, query, params=None):
        """Executa uma query e retorna os resultados"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            if query.strip().upper().startswith('SELECT'):
                rows = cursor.fetchall()
                # Converter sqlite3.Row para dict para compatibilidade
                if rows:
                    columns = [description[0] for description in cursor.description]
                    return [dict(zip(columns, row)) for row in rows]
                return []
            else:
                conn.commit()
                return cursor.rowcount
        finally:
            conn.close()
    
    def backup_database(self, backup_path):
        """Cria backup do banco de dados"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception as e:
            raise Exception(f"Erro ao criar backup: {str(e)}")
    
    def restore_database(self, backup_path):
        """Restaura banco de dados a partir de backup"""
        try:
            import shutil
            shutil.copy2(backup_path, self.db_path)
            return True
        except Exception as e:
            raise Exception(f"Erro ao restaurar backup: {str(e)}")

    # Métodos para gerenciamento de veículos
    def add_vehicle(self, user_id, vehicle_data):
        """Adiciona um novo veículo"""
        try:
            # Validar dados obrigatórios
            required_fields = ['name', 'brand', 'model', 'year']
            for field in required_fields:
                if not vehicle_data.get(field):
                    raise ValueError(f"Campo obrigatório '{field}' não fornecido")

            # Tratar placa vazia ou None
            license_plate = vehicle_data.get('license_plate')
            if license_plate:
                license_plate = license_plate.strip().upper()
                if not license_plate:
                    license_plate = None
            else:
                license_plate = None

            # Verificar se a placa já existe (apenas se não for None)
            if license_plate:
                result = self.execute_query(
                    "SELECT COUNT(*) as count FROM vehicles WHERE license_plate = ? AND user_id = ?",
                    (license_plate, user_id)
                )

                if result and len(result) > 0:
                    existing = result[0]['count']
                    if existing > 0:
                        raise ValueError(f"Já existe um veículo cadastrado com a placa '{license_plate}'")

            query = '''
                INSERT INTO vehicles (
                    user_id, name, brand, model, year, license_plate, color,
                    fuel_type, engine_size, mileage, purchase_date, purchase_price,
                    current_value, insurance_company, insurance_policy,
                    insurance_expiry, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                user_id, vehicle_data['name'], vehicle_data['brand'],
                vehicle_data['model'], vehicle_data['year'],
                license_plate, vehicle_data.get('color'),
                vehicle_data.get('fuel_type', 'gasoline'),
                vehicle_data.get('engine_size'), vehicle_data.get('mileage', 0),
                vehicle_data.get('purchase_date'), vehicle_data.get('purchase_price'),
                vehicle_data.get('current_value'), vehicle_data.get('insurance_company'),
                vehicle_data.get('insurance_policy'), vehicle_data.get('insurance_expiry'),
                vehicle_data.get('notes')
            )

            result = self.execute_query(query, params)
            return result

        except Exception as e:
            raise

    def get_user_vehicles(self, user_id, active_only=True):
        """Retorna todos os veículos do usuário"""
        query = "SELECT * FROM vehicles WHERE user_id = ?"
        if active_only:
            query += " AND is_active = 1"
        query += " ORDER BY id ASC"
        return self.execute_query(query, (user_id,))

    def update_vehicle(self, vehicle_id, vehicle_data):
        """Atualiza dados de um veículo"""
        # Tratar placa vazia ou None
        license_plate = vehicle_data.get('license_plate')
        if license_plate:
            license_plate = license_plate.strip().upper()
            if not license_plate:
                license_plate = None
        else:
            license_plate = None

        # Verificar se a placa já existe em outro veículo (apenas se não for None)
        if license_plate:
            result = self.execute_query(
                "SELECT COUNT(*) as count FROM vehicles WHERE license_plate = ? AND id != ?",
                (license_plate, vehicle_id)
            )

            if result and len(result) > 0:
                existing = result[0]['count']
                if existing > 0:
                    raise ValueError(f"Já existe outro veículo cadastrado com a placa '{license_plate}'")

        query = '''
            UPDATE vehicles SET
                name = ?, brand = ?, model = ?, year = ?, license_plate = ?,
                color = ?, fuel_type = ?, engine_size = ?, mileage = ?,
                purchase_date = ?, purchase_price = ?, current_value = ?,
                insurance_company = ?, insurance_policy = ?, insurance_expiry = ?,
                notes = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        '''
        params = (
            vehicle_data['name'], vehicle_data['brand'], vehicle_data['model'],
            vehicle_data['year'], license_plate,
            vehicle_data.get('color'), vehicle_data.get('fuel_type', 'gasoline'),
            vehicle_data.get('engine_size'), vehicle_data.get('mileage', 0),
            vehicle_data.get('purchase_date'), vehicle_data.get('purchase_price'),
            vehicle_data.get('current_value'), vehicle_data.get('insurance_company'),
            vehicle_data.get('insurance_policy'), vehicle_data.get('insurance_expiry'),
            vehicle_data.get('notes'), vehicle_id
        )
        return self.execute_query(query, params)

    def delete_vehicle(self, vehicle_id):
        """Remove um veículo (soft delete)"""
        query = "UPDATE vehicles SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        return self.execute_query(query, (vehicle_id,))

    # Métodos para gerenciamento de manutenção de veículos
    def add_maintenance_record(self, user_id, maintenance_data):
        """Adiciona um registro de manutenção"""
        # Validar dados obrigatórios
        required_fields = ['vehicle_id', 'maintenance_type', 'description', 'service_date', 'cost']
        for field in required_fields:
            if not maintenance_data.get(field):
                raise ValueError(f"Campo obrigatório '{field}' não fornecido ou vazio")

        # Validar tipos de dados
        try:
            cost = float(maintenance_data['cost'])
            if cost <= 0:
                raise ValueError("Custo deve ser maior que zero")
        except (ValueError, TypeError):
            raise ValueError("Custo deve ser um número válido maior que zero")

        # Validar vehicle_id existe
        vehicle_check = self.execute_query(
            "SELECT COUNT(*) as count FROM vehicles WHERE id = ? AND user_id = ?",
            (maintenance_data['vehicle_id'], user_id)
        )
        if not vehicle_check or vehicle_check[0]['count'] == 0:
            raise ValueError("Veículo não encontrado ou não pertence ao usuário")

        query = '''
            INSERT INTO vehicle_maintenance (
                user_id, vehicle_id, maintenance_type, description, service_date,
                mileage_at_service, cost, service_provider, next_service_date,
                next_service_mileage, warranty_period, warranty_expiry,
                receipt_number, notes, is_scheduled, is_completed
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        params = (
            user_id, maintenance_data['vehicle_id'], maintenance_data['maintenance_type'],
            maintenance_data['description'], maintenance_data['service_date'],
            maintenance_data.get('mileage_at_service'), cost,
            maintenance_data.get('service_provider'), maintenance_data.get('next_service_date'),
            maintenance_data.get('next_service_mileage'), maintenance_data.get('warranty_period'),
            maintenance_data.get('warranty_expiry'), maintenance_data.get('receipt_number'),
            maintenance_data.get('notes'), maintenance_data.get('is_scheduled', False),
            maintenance_data.get('is_completed', True)
        )
        return self.execute_query(query, params)

    def get_vehicle_maintenance(self, vehicle_id, user_id=None):
        """Retorna histórico de manutenção de um veículo"""
        query = '''
            SELECT vm.*, v.name as vehicle_name, v.brand, v.model
            FROM vehicle_maintenance vm
            JOIN vehicles v ON vm.vehicle_id = v.id
            WHERE vm.vehicle_id = ?
        '''
        params = [vehicle_id]

        if user_id:
            query += " AND vm.user_id = ?"
            params.append(user_id)

        query += " ORDER BY vm.service_date DESC"
        return self.execute_query(query, params)

    def get_upcoming_maintenance(self, user_id, days_ahead=30):
        """Retorna manutenções programadas nos próximos dias"""
        query = '''
            SELECT vm.*, v.name as vehicle_name, v.brand, v.model
            FROM vehicle_maintenance vm
            JOIN vehicles v ON vm.vehicle_id = v.id
            WHERE vm.user_id = ? AND vm.is_scheduled = 1 AND vm.is_completed = 0
            AND vm.next_service_date <= date('now', '+{} days')
            ORDER BY vm.next_service_date
        '''.format(days_ahead)
        return self.execute_query(query, (user_id,))

    def update_maintenance_record(self, maintenance_id, maintenance_data):
        """Atualiza um registro de manutenção"""
        query = '''
            UPDATE vehicle_maintenance SET
                maintenance_type = ?, description = ?, service_date = ?,
                mileage_at_service = ?, cost = ?, service_provider = ?,
                next_service_date = ?, next_service_mileage = ?, warranty_period = ?,
                warranty_expiry = ?, receipt_number = ?, notes = ?,
                is_scheduled = ?, is_completed = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        '''
        params = (
            maintenance_data['maintenance_type'], maintenance_data['description'],
            maintenance_data['service_date'], maintenance_data.get('mileage_at_service'),
            maintenance_data['cost'], maintenance_data.get('service_provider'),
            maintenance_data.get('next_service_date'), maintenance_data.get('next_service_mileage'),
            maintenance_data.get('warranty_period'), maintenance_data.get('warranty_expiry'),
            maintenance_data.get('receipt_number'), maintenance_data.get('notes'),
            maintenance_data.get('is_scheduled', False), maintenance_data.get('is_completed', True),
            maintenance_id
        )
        return self.execute_query(query, params)

    def delete_maintenance_record(self, maintenance_id):
        """Remove um registro de manutenção"""
        query = "DELETE FROM vehicle_maintenance WHERE id = ?"
        return self.execute_query(query, (maintenance_id,))

    def get_maintenance_alerts(self, user_id, days_ahead=30):
        """Retorna alertas de manutenção baseados em data e quilometragem"""
        try:
            today = date.today()
            alert_date = today + timedelta(days=days_ahead)

            # Alertas por data
            date_alerts_query = '''
                SELECT
                    vm.id,
                    vm.vehicle_id,
                    vm.maintenance_type,
                    vm.description,
                    vm.next_service_date,
                    vm.next_service_mileage,
                    v.name as vehicle_name,
                    v.brand,
                    v.model,
                    v.mileage as current_mileage,
                    'date' as alert_type,
                    (julianday(vm.next_service_date) - julianday('now')) as days_until
                FROM vehicle_maintenance vm
                JOIN vehicles v ON vm.vehicle_id = v.id
                WHERE vm.user_id = ?
                AND vm.next_service_date IS NOT NULL
                AND vm.next_service_date <= ?
                AND vm.is_completed = 1
                AND vm.is_scheduled = 0
                AND v.is_active = 1
            '''

            # Alertas por quilometragem
            mileage_alerts_query = '''
                SELECT
                    vm.id,
                    vm.vehicle_id,
                    vm.maintenance_type,
                    vm.description,
                    vm.next_service_date,
                    vm.next_service_mileage,
                    v.name as vehicle_name,
                    v.brand,
                    v.model,
                    v.mileage as current_mileage,
                    'mileage' as alert_type,
                    (vm.next_service_mileage - v.mileage) as km_remaining
                FROM vehicle_maintenance vm
                JOIN vehicles v ON vm.vehicle_id = v.id
                WHERE vm.user_id = ?
                AND vm.next_service_mileage IS NOT NULL
                AND v.mileage >= (vm.next_service_mileage - 1000)
                AND vm.is_completed = 1
                AND vm.is_scheduled = 0
                AND v.is_active = 1
            '''

            date_alerts = self.execute_query(date_alerts_query, (user_id, alert_date.isoformat()))
            mileage_alerts = self.execute_query(mileage_alerts_query, (user_id,))

            # Combinar alertas
            all_alerts = []
            if date_alerts:
                all_alerts.extend(date_alerts)
            if mileage_alerts:
                all_alerts.extend(mileage_alerts)

            return all_alerts

        except Exception as e:
            print(f"Erro ao buscar alertas de manutenção: {e}")
            return []

    def create_automatic_maintenance_reminder(self, maintenance_record, reminder_type):
        """Cria lembrete automático de manutenção"""
        try:
            reminder_data = {
                'vehicle_id': maintenance_record['vehicle_id'],
                'maintenance_type': maintenance_record['maintenance_type'],
                'description': f"🔔 Lembrete: {maintenance_record['description']} (baseado em {reminder_type})",
                'service_date': date.today().isoformat(),
                'cost': 0.0,
                'is_scheduled': True,
                'is_completed': False,
                'notes': f"Lembrete criado automaticamente em {date.today().strftime('%d/%m/%Y')} baseado em {reminder_type}"
            }

            return self.add_maintenance_record(maintenance_record['user_id'], reminder_data)

        except Exception as e:
            print(f"Erro ao criar lembrete automático: {e}")
            return None

    def get_overdue_maintenance(self, user_id):
        """Retorna manutenções em atraso"""
        today = date.today()

        query = '''
            SELECT
                vm.*,
                v.name as vehicle_name,
                v.brand,
                v.model,
                v.mileage as current_mileage,
                (julianday('now') - julianday(vm.next_service_date)) as days_overdue
            FROM vehicle_maintenance vm
            JOIN vehicles v ON vm.vehicle_id = v.id
            WHERE vm.user_id = ?
            AND vm.next_service_date IS NOT NULL
            AND vm.next_service_date < ?
            AND vm.is_completed = 1
            AND vm.is_scheduled = 0
            AND v.is_active = 1
            ORDER BY vm.next_service_date ASC
        '''

        return self.execute_query(query, (user_id, today.isoformat()))

    def get_maintenance_history_summary(self, vehicle_id, user_id, months=12):
        """Retorna resumo do histórico de manutenção"""
        query = '''
            SELECT
                maintenance_type,
                COUNT(*) as total_services,
                SUM(cost) as total_cost,
                AVG(cost) as avg_cost,
                MIN(service_date) as first_service,
                MAX(service_date) as last_service,
                AVG(julianday(service_date) - julianday(LAG(service_date) OVER (
                    PARTITION BY maintenance_type ORDER BY service_date
                ))) as avg_days_between
            FROM vehicle_maintenance
            WHERE vehicle_id = ? AND user_id = ?
            AND service_date >= date('now', '-{} months')
            AND is_completed = 1
            GROUP BY maintenance_type
            ORDER BY total_cost DESC
        '''.format(months)

        return self.execute_query(query, (vehicle_id, user_id))

    def get_insurance_expiry_alerts(self, user_id, days_ahead=30):
        """Retorna alertas de vencimento de seguros e documentos"""
        try:
            today = date.today()
            alert_date = today + timedelta(days=days_ahead)

            query = '''
                SELECT
                    id,
                    name as vehicle_name,
                    brand,
                    model,
                    license_plate,
                    insurance_company,
                    insurance_policy,
                    insurance_expiry,
                    (julianday(insurance_expiry) - julianday('now')) as days_until_expiry,
                    CASE
                        WHEN insurance_expiry < date('now') THEN 'VENCIDO'
                        WHEN insurance_expiry <= date('now', '+7 days') THEN 'VENCE EM BREVE'
                        WHEN insurance_expiry <= date('now', '+30 days') THEN 'VENCIMENTO PRÓXIMO'
                        ELSE 'OK'
                    END as alert_level
                FROM vehicles
                WHERE user_id = ?
                AND insurance_expiry IS NOT NULL
                AND insurance_expiry <= ?
                AND is_active = 1
                ORDER BY insurance_expiry ASC
            '''

            return self.execute_query(query, (user_id, alert_date.isoformat()))

        except Exception as e:
            print(f"Erro ao buscar alertas de seguro: {e}")
            return []

    def get_expired_documents(self, user_id):
        """Retorna documentos já vencidos"""
        today = date.today()

        query = '''
            SELECT
                id,
                name as vehicle_name,
                brand,
                model,
                license_plate,
                insurance_company,
                insurance_expiry,
                (julianday('now') - julianday(insurance_expiry)) as days_expired
            FROM vehicles
            WHERE user_id = ?
            AND insurance_expiry IS NOT NULL
            AND insurance_expiry < ?
            AND is_active = 1
            ORDER BY insurance_expiry ASC
        '''

        return self.execute_query(query, (user_id, today.isoformat()))

    def update_insurance_info(self, vehicle_id, insurance_data):
        """Atualiza informações de seguro de um veículo"""
        query = '''
            UPDATE vehicles SET
                insurance_company = ?,
                insurance_policy = ?,
                insurance_expiry = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        '''
        params = (
            insurance_data.get('insurance_company'),
            insurance_data.get('insurance_policy'),
            insurance_data.get('insurance_expiry'),
            vehicle_id
        )

        return self.execute_query(query, params)

    def get_vehicle_document_summary(self, user_id):
        """Retorna resumo dos documentos de todos os veículos"""
        query = '''
            SELECT
                v.id,
                v.name as vehicle_name,
                v.brand,
                v.model,
                v.license_plate,
                v.insurance_company,
                v.insurance_expiry,
                CASE
                    WHEN v.insurance_expiry IS NULL THEN 'NÃO INFORMADO'
                    WHEN v.insurance_expiry < date('now') THEN 'VENCIDO'
                    WHEN v.insurance_expiry <= date('now', '+30 days') THEN 'VENCE EM BREVE'
                    ELSE 'OK'
                END as insurance_status,
                (julianday(v.insurance_expiry) - julianday('now')) as days_until_expiry
            FROM vehicles v
            WHERE v.user_id = ? AND v.is_active = 1
            ORDER BY v.insurance_expiry ASC NULLS LAST
        '''

        return self.execute_query(query, (user_id,))

    def create_insurance_renewal_reminder(self, vehicle_id, user_id, days_before=30):
        """Cria lembrete de renovação de seguro"""
        try:
            # Buscar dados do veículo
            vehicle_query = '''
                SELECT name, insurance_company, insurance_expiry
                FROM vehicles
                WHERE id = ? AND user_id = ?
            '''
            vehicle_result = self.execute_query(vehicle_query, (vehicle_id, user_id))

            if not vehicle_result:
                return None

            vehicle = vehicle_result[0]

            # Criar lembrete de manutenção para renovação de seguro
            reminder_data = {
                'vehicle_id': vehicle_id,
                'maintenance_type': 'insurance_renewal',
                'description': f"🛡️ Renovar seguro - {vehicle['insurance_company']}",
                'service_date': date.today().isoformat(),
                'cost': 0.0,
                'is_scheduled': True,
                'is_completed': False,
                'notes': f"Lembrete de renovação de seguro. Vencimento: {vehicle['insurance_expiry']}"
            }

            return self.add_maintenance_record(user_id, reminder_data)

        except Exception as e:
            print(f"Erro ao criar lembrete de renovação: {e}")
            return None

    # Métodos para gerenciamento de registros de combustível
    def add_fuel_record(self, user_id, fuel_data):
        """Adiciona um registro de abastecimento"""
        # Validar dados obrigatórios
        required_fields = ['vehicle_id', 'fuel_date', 'fuel_type', 'liters', 'price_per_liter', 'total_cost']
        for field in required_fields:
            if not fuel_data.get(field):
                raise ValueError(f"Campo obrigatório '{field}' não fornecido ou vazio")

        # Validar tipos de dados numéricos
        try:
            liters = float(fuel_data['liters'])
            price_per_liter = float(fuel_data['price_per_liter'])
            total_cost = float(fuel_data['total_cost'])

            if liters <= 0:
                raise ValueError("Quantidade de litros deve ser maior que zero")
            if price_per_liter <= 0:
                raise ValueError("Preço por litro deve ser maior que zero")
            if total_cost <= 0:
                raise ValueError("Custo total deve ser maior que zero")

        except (ValueError, TypeError) as e:
            if "could not convert" in str(e) or "invalid literal" in str(e):
                raise ValueError("Valores numéricos inválidos (litros, preço, custo)")
            raise

        # Validar vehicle_id existe
        vehicle_check = self.execute_query(
            "SELECT COUNT(*) as count FROM vehicles WHERE id = ? AND user_id = ?",
            (fuel_data['vehicle_id'], user_id)
        )
        if not vehicle_check or vehicle_check[0]['count'] == 0:
            raise ValueError("Veículo não encontrado ou não pertence ao usuário")

        # Calcular eficiência se possível
        fuel_efficiency = self.calculate_fuel_efficiency(
            fuel_data['vehicle_id'],
            fuel_data.get('mileage'),
            liters
        )

        query = '''
            INSERT INTO fuel_records (
                user_id, vehicle_id, fuel_date, fuel_type, liters,
                price_per_liter, total_cost, mileage, gas_station,
                location, is_full_tank, fuel_efficiency, notes, receipt_number
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        params = (
            user_id, fuel_data['vehicle_id'], fuel_data['fuel_date'],
            fuel_data['fuel_type'], liters, price_per_liter,
            total_cost, fuel_data.get('mileage'),
            fuel_data.get('gas_station'), fuel_data.get('location'),
            fuel_data.get('is_full_tank', True), fuel_efficiency,
            fuel_data.get('notes'), fuel_data.get('receipt_number')
        )

        record_id = self.execute_query(query, params)

        # Atualizar quilometragem do veículo se fornecida
        if fuel_data.get('mileage'):
            self.update_vehicle_mileage(fuel_data['vehicle_id'], fuel_data['mileage'])

        # Recalcular eficiência de registros anteriores se necessário
        self.recalculate_fuel_efficiency_for_vehicle(fuel_data['vehicle_id'])

        return record_id

    def get_vehicle_fuel_records(self, vehicle_id, user_id=None, limit=None):
        """Retorna registros de combustível de um veículo"""
        query = '''
            SELECT fr.*, v.name as vehicle_name, v.brand, v.model
            FROM fuel_records fr
            JOIN vehicles v ON fr.vehicle_id = v.id
            WHERE fr.vehicle_id = ?
        '''
        params = [vehicle_id]

        if user_id:
            query += " AND fr.user_id = ?"
            params.append(user_id)

        query += " ORDER BY fr.fuel_date DESC, fr.mileage DESC"

        if limit:
            query += f" LIMIT {limit}"

        return self.execute_query(query, params)

    def calculate_fuel_efficiency(self, vehicle_id, current_mileage, liters):
        """Calcula a eficiência de combustível baseada no último abastecimento"""
        if not current_mileage or not liters:
            return None

        # Buscar último abastecimento com tanque cheio
        query = '''
            SELECT mileage FROM fuel_records
            WHERE vehicle_id = ? AND is_full_tank = 1 AND mileage < ?
            ORDER BY fuel_date DESC, mileage DESC LIMIT 1
        '''
        result = self.execute_query(query, (vehicle_id, current_mileage))

        if result and len(result) > 0:
            last_mileage = result[0]['mileage']
            if last_mileage and current_mileage > last_mileage:
                distance = current_mileage - last_mileage
                return round(distance / liters, 2)

        return None

    def get_fuel_statistics(self, vehicle_id, user_id, months=12):
        """Retorna estatísticas de combustível dos últimos meses"""
        query = '''
            SELECT
                COUNT(*) as total_records,
                SUM(liters) as total_liters,
                SUM(total_cost) as total_cost,
                AVG(price_per_liter) as avg_price_per_liter,
                AVG(fuel_efficiency) as avg_efficiency,
                MIN(fuel_date) as first_record,
                MAX(fuel_date) as last_record
            FROM fuel_records
            WHERE vehicle_id = ? AND user_id = ?
            AND fuel_date >= date('now', '-{} months')
        '''.format(months)

        return self.execute_query(query, (vehicle_id, user_id))

    def update_fuel_record(self, record_id, fuel_data):
        """Atualiza um registro de combustível"""
        # Recalcular eficiência se necessário
        fuel_efficiency = self.calculate_fuel_efficiency(
            fuel_data['vehicle_id'],
            fuel_data.get('mileage'),
            fuel_data['liters']
        )

        query = '''
            UPDATE fuel_records SET
                fuel_date = ?, fuel_type = ?, liters = ?, price_per_liter = ?,
                total_cost = ?, mileage = ?, gas_station = ?, location = ?,
                is_full_tank = ?, fuel_efficiency = ?, notes = ?,
                receipt_number = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        '''
        params = (
            fuel_data['fuel_date'], fuel_data['fuel_type'], fuel_data['liters'],
            fuel_data['price_per_liter'], fuel_data['total_cost'],
            fuel_data.get('mileage'), fuel_data.get('gas_station'),
            fuel_data.get('location'), fuel_data.get('is_full_tank', True),
            fuel_efficiency, fuel_data.get('notes'), fuel_data.get('receipt_number'),
            record_id
        )

        result = self.execute_query(query, params)

        # Atualizar quilometragem do veículo se fornecida
        if fuel_data.get('mileage'):
            self.update_vehicle_mileage(fuel_data['vehicle_id'], fuel_data['mileage'])

        # Recalcular eficiência de registros relacionados
        self.recalculate_fuel_efficiency_for_vehicle(fuel_data['vehicle_id'])

        return result

    def update_vehicle_mileage(self, vehicle_id, new_mileage):
        """Atualiza a quilometragem atual do veículo"""
        try:
            # Verificar se a nova quilometragem é maior que a atual
            current_query = "SELECT mileage FROM vehicles WHERE id = ?"
            result = self.execute_query(current_query, (vehicle_id,))

            if result and len(result) > 0:
                current_mileage = result[0]['mileage'] or 0
                if new_mileage > current_mileage:
                    update_query = '''
                        UPDATE vehicles
                        SET mileage = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    '''
                    self.execute_query(update_query, (new_mileage, vehicle_id))
        except Exception as e:
            print(f"Erro ao atualizar quilometragem do veículo: {e}")

    def recalculate_fuel_efficiency_for_vehicle(self, vehicle_id):
        """Recalcula eficiência de combustível para todos os registros de um veículo"""
        try:
            # Buscar todos os registros do veículo ordenados por data e quilometragem
            query = '''
                SELECT id, mileage, liters, fuel_date, is_full_tank
                FROM fuel_records
                WHERE vehicle_id = ? AND mileage IS NOT NULL AND liters > 0
                ORDER BY fuel_date ASC, mileage ASC
            '''
            records = self.execute_query(query, (vehicle_id,))

            if not records or len(records) < 2:
                return

            # Recalcular eficiência para cada registro
            for i, record in enumerate(records):
                if not record['is_full_tank']:
                    continue

                # Encontrar último abastecimento com tanque cheio anterior
                previous_full_tank = None
                for j in range(i - 1, -1, -1):
                    if records[j]['is_full_tank'] and records[j]['mileage'] < record['mileage']:
                        previous_full_tank = records[j]
                        break

                if previous_full_tank:
                    distance = record['mileage'] - previous_full_tank['mileage']
                    if distance > 0:
                        efficiency = round(distance / record['liters'], 2)

                        # Atualizar eficiência no banco
                        update_query = '''
                            UPDATE fuel_records
                            SET fuel_efficiency = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        '''
                        self.execute_query(update_query, (efficiency, record['id']))

        except Exception as e:
            print(f"Erro ao recalcular eficiência de combustível: {e}")

    def get_fuel_efficiency_trends(self, vehicle_id, user_id, months=6):
        """Retorna tendências de eficiência de combustível"""
        query = '''
            SELECT
                strftime('%Y-%m', fuel_date) as month,
                AVG(fuel_efficiency) as avg_efficiency,
                COUNT(*) as records_count,
                MIN(fuel_efficiency) as min_efficiency,
                MAX(fuel_efficiency) as max_efficiency
            FROM fuel_records
            WHERE vehicle_id = ? AND user_id = ?
            AND fuel_efficiency IS NOT NULL
            AND fuel_date >= date('now', '-{} months')
            GROUP BY strftime('%Y-%m', fuel_date)
            ORDER BY month DESC
        '''.format(months)

        return self.execute_query(query, (vehicle_id, user_id))

    def get_low_efficiency_alerts(self, user_id, efficiency_threshold=8.0):
        """Retorna alertas de baixa eficiência de combustível"""
        query = '''
            SELECT
                v.name as vehicle_name,
                v.brand,
                v.model,
                fr.fuel_date,
                fr.fuel_efficiency,
                AVG(fr2.fuel_efficiency) as avg_efficiency
            FROM fuel_records fr
            JOIN vehicles v ON fr.vehicle_id = v.id
            JOIN fuel_records fr2 ON fr2.vehicle_id = fr.vehicle_id
                AND fr2.fuel_date >= date(fr.fuel_date, '-3 months')
                AND fr2.fuel_efficiency IS NOT NULL
            WHERE fr.user_id = ?
            AND fr.fuel_efficiency IS NOT NULL
            AND fr.fuel_efficiency < ?
            AND fr.fuel_date >= date('now', '-1 month')
            GROUP BY fr.id
            HAVING fr.fuel_efficiency < (avg_efficiency * 0.8)
            ORDER BY fr.fuel_date DESC
        '''

        return self.execute_query(query, (user_id, efficiency_threshold))

    def delete_fuel_record(self, record_id):
        """Remove um registro de combustível"""
        # Buscar dados do registro antes de deletar para recalcular eficiência
        record_query = "SELECT vehicle_id FROM fuel_records WHERE id = ?"
        record_result = self.execute_query(record_query, (record_id,))

        result = self.execute_query("DELETE FROM fuel_records WHERE id = ?", (record_id,))

        # Recalcular eficiência do veículo após remoção
        if record_result and len(record_result) > 0:
            vehicle_id = record_result[0]['vehicle_id']
            self.recalculate_fuel_efficiency_for_vehicle(vehicle_id)

        return result

    def execute_query_with_id(self, query, params=None):
        """Executa query e retorna o ID da inserção"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row

        try:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            conn.commit()
            return cursor.lastrowid
        finally:
            conn.close()
