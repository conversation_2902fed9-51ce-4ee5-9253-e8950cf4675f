#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug da limpeza de manutenção
"""

import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager

def debug_manutencao():
    """Debug da limpeza de manutenção"""
    print("🔍 DEBUG DA LIMPEZA DE MANUTENÇÃO")
    print("=" * 50)
    
    try:
        # Inicializar sistema
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login: {user_data['full_name']}")
        user_id = user_data['id']
        
        # 1. Verificar estrutura da tabela de manutenção
        print(f"\n📊 VERIFICANDO ESTRUTURA DA TABELA...")
        
        try:
            # Verificar se tabela existe
            result = db_manager.execute_query("SELECT name FROM sqlite_master WHERE type='table' AND name='vehicle_maintenance'")
            if result:
                print(f"   ✅ Tabela 'vehicle_maintenance' existe")
                
                # Mostrar estrutura
                try:
                    result = db_manager.execute_query("PRAGMA table_info(vehicle_maintenance)")
                    print(f"   📋 Colunas da tabela:")
                    if result:
                        for col in result:
                            if isinstance(col, dict):
                                print(f"      • {col.get('name', 'N/A')} ({col.get('type', 'N/A')})")
                            else:
                                print(f"      • {col}")
                    else:
                        print(f"      Nenhuma coluna encontrada")
                except Exception as e:
                    print(f"      ❌ Erro ao mostrar colunas: {str(e)}")
            else:
                print(f"   ❌ Tabela 'vehicle_maintenance' NÃO existe")
                return
        except Exception as e:
            print(f"   ❌ Erro ao verificar tabela: {str(e)}")
            return
        
        # 2. Listar TODAS as manutenções do usuário
        print(f"\n📋 TODAS AS MANUTENÇÕES DO USUÁRIO:")
        
        try:
            all_maintenance = db_manager.execute_query("""
                SELECT vm.id, vm.description, vm.maintenance_type, vm.cost, vm.service_date,
                       v.id as vehicle_id, v.name as vehicle_name, v.brand, v.model
                FROM vehicle_maintenance vm
                JOIN vehicles v ON vm.vehicle_id = v.id
                WHERE vm.user_id = ?
                ORDER BY vm.service_date DESC
            """, (user_id,))
            
            print(f"   📊 Total de manutenções: {len(all_maintenance)}")
            
            for i, maint in enumerate(all_maintenance, 1):
                print(f"   {i:2d}. ID {maint['id']}: {maint['description']}")
                print(f"       Veículo: {maint['vehicle_name']} ({maint['brand']} {maint['model']})")
                print(f"       Tipo: {maint['maintenance_type']}, Custo: R$ {maint['cost']:.2f}")
                print(f"       Data: {maint['service_date']}")
                print()
                
        except Exception as e:
            print(f"   ❌ Erro ao listar manutenções: {str(e)}")
            return
        
        # 3. Testar query de detecção de manutenções de teste
        print(f"\n🔍 TESTANDO QUERY DE DETECÇÃO DE MANUTENÇÕES DE TESTE...")
        
        # Query atual usada no sistema
        try:
            test_maintenance_query = """
                SELECT COUNT(*) as count FROM vehicle_maintenance vm
                JOIN vehicles v ON vm.vehicle_id = v.id
                WHERE vm.user_id = ? AND (
                    vm.description LIKE '%teste%' OR 
                    vm.description LIKE '%Teste%' OR
                    vm.description LIKE '%TESTE%' OR
                    v.name LIKE '%teste%' OR
                    v.name LIKE '%Teste%' OR
                    v.name LIKE '%TESTE%'
                )
            """
            result = db_manager.execute_query(test_maintenance_query, (user_id,))
            test_count = result[0]['count'] if result else 0
            
            print(f"   📊 Manutenções de teste encontradas (query atual): {test_count}")
            
        except Exception as e:
            print(f"   ❌ Erro na query de teste: {str(e)}")
        
        # 4. Testar queries alternativas
        print(f"\n🧪 TESTANDO QUERIES ALTERNATIVAS...")
        
        # Query 1: Só por descrição
        try:
            alt_query1 = """
                SELECT vm.id, vm.description, v.name as vehicle_name
                FROM vehicle_maintenance vm
                JOIN vehicles v ON vm.vehicle_id = v.id
                WHERE vm.user_id = ? AND (
                    vm.description LIKE '%teste%' OR 
                    vm.description LIKE '%Teste%' OR
                    vm.description LIKE '%TESTE%'
                )
            """
            result = db_manager.execute_query(alt_query1, (user_id,))
            print(f"   🔍 Query 1 (só descrição): {len(result)} resultados")
            for r in result:
                print(f"      • ID {r['id']}: {r['description']} (Veículo: {r['vehicle_name']})")
                
        except Exception as e:
            print(f"   ❌ Erro na query alternativa 1: {str(e)}")
        
        # Query 2: Só por veículo
        try:
            alt_query2 = """
                SELECT vm.id, vm.description, v.name as vehicle_name
                FROM vehicle_maintenance vm
                JOIN vehicles v ON vm.vehicle_id = v.id
                WHERE vm.user_id = ? AND (
                    v.name LIKE '%teste%' OR
                    v.name LIKE '%Teste%' OR
                    v.name LIKE '%TESTE%'
                )
            """
            result = db_manager.execute_query(alt_query2, (user_id,))
            print(f"   🔍 Query 2 (só veículo): {len(result)} resultados")
            for r in result:
                print(f"      • ID {r['id']}: {r['description']} (Veículo: {r['vehicle_name']})")
                
        except Exception as e:
            print(f"   ❌ Erro na query alternativa 2: {str(e)}")
        
        # Query 3: Expandida com mais padrões
        try:
            alt_query3 = """
                SELECT vm.id, vm.description, v.name as vehicle_name, vm.maintenance_type
                FROM vehicle_maintenance vm
                JOIN vehicles v ON vm.vehicle_id = v.id
                WHERE vm.user_id = ? AND (
                    vm.description LIKE '%teste%' OR 
                    vm.description LIKE '%Teste%' OR
                    vm.description LIKE '%TESTE%' OR
                    vm.description LIKE '%test%' OR
                    vm.description LIKE '%Test%' OR
                    vm.description LIKE '%TEST%' OR
                    vm.maintenance_type LIKE '%test%' OR
                    v.name LIKE '%teste%' OR
                    v.name LIKE '%Teste%' OR
                    v.name LIKE '%TESTE%' OR
                    v.brand LIKE '%teste%' OR
                    v.model LIKE '%teste%' OR
                    v.license_plate LIKE '%TEST%' OR
                    v.license_plate LIKE '%TST%'
                )
            """
            result = db_manager.execute_query(alt_query3, (user_id,))
            print(f"   🔍 Query 3 (expandida): {len(result)} resultados")
            for r in result:
                print(f"      • ID {r['id']}: {r['description']} (Veículo: {r['vehicle_name']}, Tipo: {r['maintenance_type']})")
                
        except Exception as e:
            print(f"   ❌ Erro na query alternativa 3: {str(e)}")
        
        # 5. Verificar veículos de teste
        print(f"\n🚗 VERIFICANDO VEÍCULOS DE TESTE...")
        
        try:
            test_vehicles = db_manager.execute_query("""
                SELECT id, name, brand, model, license_plate
                FROM vehicles
                WHERE user_id = ? AND (
                    name LIKE '%teste%' OR 
                    name LIKE '%Teste%' OR
                    name LIKE '%TESTE%' OR
                    brand LIKE '%teste%' OR
                    model LIKE '%teste%' OR
                    license_plate LIKE '%TEST%' OR
                    license_plate LIKE '%TST%'
                )
            """, (user_id,))
            
            print(f"   📊 Veículos de teste encontrados: {len(test_vehicles)}")
            for vehicle in test_vehicles:
                print(f"      • ID {vehicle['id']}: {vehicle['name']} - {vehicle['brand']} {vehicle['model']}")
                if vehicle['license_plate']:
                    print(f"        Placa: {vehicle['license_plate']}")
                
                # Verificar manutenções deste veículo
                vehicle_maintenance = db_manager.execute_query("""
                    SELECT id, description, maintenance_type, cost
                    FROM vehicle_maintenance
                    WHERE vehicle_id = ? AND user_id = ?
                """, (vehicle['id'], user_id))
                
                print(f"        Manutenções: {len(vehicle_maintenance)}")
                for maint in vehicle_maintenance:
                    print(f"          - ID {maint['id']}: {maint['description']} (R$ {maint['cost']:.2f})")
                print()
                
        except Exception as e:
            print(f"   ❌ Erro ao verificar veículos de teste: {str(e)}")
        
        print(f"\n✅ DEBUG CONCLUÍDO!")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_manutencao()
