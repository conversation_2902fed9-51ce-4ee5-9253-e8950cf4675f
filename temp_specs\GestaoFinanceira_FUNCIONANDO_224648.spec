# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['..\\executar_terminal_sem_emoji.py'],
    pathex=[],
    binaries=[],
    datas=[('src', 'src'), ('config', 'config'), ('assets', 'assets'), ('data', 'data')],
    hiddenimports=['tkinter.simpledialog', 'tkinter.filedialog', 'tkinter.colorchooser', 'tkinter.font', 'tkinter.messagebox', 'tkcalendar', 'sqlite3', 'datetime', 'dateutil', 'dateutil.parser', 'dateutil.relativedelta'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='GestaoFinanceira_FUNCIONANDO_224648',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['assets\\gestao_financeira.ico'],
)
