#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Executar o sistema com a dashboard modernizada
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Função principal para executar o sistema com dashboard moderna"""
    
    print("=" * 70)
    print("🎨 SISTEMA DE GESTÃO FINANCEIRA - DASHBOARD MODERNIZADA")
    print("=" * 70)
    
    try:
        # Importar módulos
        from database import DatabaseManager
        from auth import AuthManager
        from gui.login_window import LoginWindow
        
        print("✅ Módulos importados com sucesso!")
        
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        auth_manager = AuthManager(db_manager)
        
        print("✅ Banco de dados inicializado!")
        
        # <PERSON>riar janela principal
        root = tk.Tk()
        root.withdraw()  # Esconder janela principal inicialmente
        
        print("🎨 Iniciando sistema com dashboard modernizada...")
        
        # Mostrar informações sobre as melhorias
        print("\n🎉 MELHORIAS IMPLEMENTADAS:")
        print("• 🎨 Design moderno e profissional")
        print("• 💳 Cards financeiros com visual card-style")
        print("• 🌈 Paleta de cores harmoniosa")
        print("• 📊 Layout otimizado e responsivo")
        print("• ✨ Ícones emoji para melhor UX")
        print("• 📱 Tipografia Segoe UI moderna")
        print("• 🎯 Informações organizadas e claras")
        
        # Criar janela de login
        login_window = LoginWindow(root, db_manager, auth_manager)
        
        print("\n🔐 Janela de login criada!")
        print("👤 Use as credenciais: admin / admin123")
        print("🖥️ Executando sistema...")
        
        # Executar aplicação
        root.mainloop()
        
    except Exception as e:
        print(f"\n❌ Erro ao executar sistema: {e}")
        import traceback
        traceback.print_exc()
        
        # Mostrar erro em messagebox se possível
        try:
            messagebox.showerror("Erro", f"Erro ao executar sistema:\n{str(e)}")
        except:
            pass

if __name__ == "__main__":
    main()
    
    print("\n" + "=" * 70)
    print("📋 RESUMO DAS MELHORIAS NA DASHBOARD")
    print("=" * 70)
    
    print("""
🎨 VISUAL MODERNIZADO:
• Cores profissionais e harmoniosas
• Cards com sombras e bordas coloridas
• Ícones emoji para identificação rápida
• Tipografia Segoe UI limpa e moderna

💳 CARDS FINANCEIROS:
• 💰 Saldo Total - Roxo (#8E44AD)
• 📈 Receitas do Mês - Verde (#27AE60)
• 📉 Despesas do Mês - Vermelho (#E74C3C)
• ⚖️ Saldo do Mês - Azul (#3498DB)

📊 LAYOUT APRIMORADO:
• Cabeçalho com título e data atual
• Seções bem definidas com cores
• Espaçamento otimizado
• Design responsivo

📱 LISTAS MODERNAS:
• Transações com ícones e cores
• Alertas com barras coloridas laterais
• Informações organizadas
• Visual limpo e profissional

🎯 EXPERIÊNCIA DO USUÁRIO:
• Interface mais intuitiva
• Informações mais claras
• Visual mais atrativo
• Navegação melhorada
    """)
    
    print("🎉 Dashboard modernizada implementada com sucesso!")
    print("💫 Aproveite a nova experiência visual!")
