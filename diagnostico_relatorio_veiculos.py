#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnóstico do erro no relatório de veículos
"""

import sys
import os
import sqlite3

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager

def diagnostico_relatorio_veiculos():
    """Diagnostica problemas no relatório de veículos"""
    print("🔍 DIAGNÓSTICO DO RELATÓRIO DE VEÍCULOS")
    print("=" * 60)
    
    try:
        # 1. Verificar banco de dados
        print("📊 VERIFICANDO BANCO DE DADOS...")
        
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Conectar diretamente ao SQLite para verificar tabelas
        conn = sqlite3.connect('financial_system.db')
        cursor = conn.cursor()
        
        # Listar todas as tabelas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📋 Tabelas encontradas no banco:")
        for table in tables:
            print(f"   • {table[0]}")
        
        # Verificar tabelas específicas de veículos
        vehicle_tables = ['vehicles', 'fuel_records', 'maintenance_records', 'vehicle_maintenance']
        
        print(f"\n🚗 VERIFICANDO TABELAS DE VEÍCULOS...")
        for table_name in vehicle_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table_name}: {count} registros")
                
                # Mostrar estrutura da tabela
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                print(f"      Colunas: {', '.join([col[1] for col in columns])}")
                
            except sqlite3.OperationalError as e:
                print(f"   ❌ {table_name}: ERRO - {str(e)}")
        
        conn.close()
        
        # 2. Testar autenticação
        print(f"\n🔐 TESTANDO AUTENTICAÇÃO...")
        
        auth_manager = AuthManager(db_manager)
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        
        if user_data:
            print(f"   ✅ Login realizado: {user_data['full_name']}")
            user_id = user_data['id']
        else:
            print(f"   ❌ Falha no login!")
            return
        
        # 3. Testar queries do relatório
        print(f"\n📊 TESTANDO QUERIES DO RELATÓRIO...")
        
        # Query de veículos
        try:
            vehicles_query = """
                SELECT id, brand, model, year, license_plate
                FROM vehicles
                WHERE user_id = ?
                ORDER BY brand, model
            """
            vehicles = db_manager.execute_query(vehicles_query, (user_id,))
            print(f"   ✅ Query de veículos: {len(vehicles)} veículos encontrados")
            
            for vehicle in vehicles:
                print(f"      • ID {vehicle['id']}: {vehicle['brand']} {vehicle['model']} ({vehicle['year']})")
                
        except Exception as e:
            print(f"   ❌ Erro na query de veículos: {str(e)}")
            return
        
        # Se não há veículos, criar um para teste
        if len(vehicles) == 0:
            print(f"\n🔧 CRIANDO VEÍCULO DE TESTE...")
            try:
                # Verificar se existe método para criar veículo
                if hasattr(db_manager, 'create_vehicle'):
                    vehicle_id = db_manager.create_vehicle(
                        user_id=user_id,
                        name="Carro Teste",
                        brand="Toyota",
                        model="Corolla",
                        year=2020,
                        license_plate="ABC-1234",
                        fuel_type="Gasolina"
                    )
                    print(f"   ✅ Veículo de teste criado com ID: {vehicle_id}")
                else:
                    # Criar manualmente
                    insert_query = """
                        INSERT INTO vehicles (user_id, name, brand, model, year, license_plate, fuel_type)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """
                    db_manager.execute_query(insert_query, (
                        user_id, "Carro Teste", "Toyota", "Corolla", 2020, "ABC-1234", "Gasolina"
                    ))
                    print(f"   ✅ Veículo de teste criado manualmente")
                
                # Recarregar veículos
                vehicles = db_manager.execute_query(vehicles_query, (user_id,))
                print(f"   📊 Veículos após criação: {len(vehicles)}")
                
            except Exception as e:
                print(f"   ❌ Erro ao criar veículo de teste: {str(e)}")
        
        # Testar queries de combustível e manutenção para cada veículo
        for vehicle in vehicles:
            vehicle_id = vehicle['id']
            vehicle_name = f"{vehicle['brand']} {vehicle['model']}"
            
            print(f"\n🔧 Testando queries para {vehicle_name} (ID: {vehicle_id})...")
            
            # Query de combustível
            try:
                fuel_query = """
                    SELECT COALESCE(SUM(total_cost), 0) as fuel_cost
                    FROM fuel_records
                    WHERE vehicle_id = ?
                """
                fuel_result = db_manager.execute_query(fuel_query, (vehicle_id,))
                fuel_cost = fuel_result[0]['fuel_cost'] if fuel_result else 0
                print(f"   ✅ Combustível: R$ {fuel_cost:,.2f}")
                
            except Exception as e:
                print(f"   ❌ Erro na query de combustível: {str(e)}")
            
            # Query de manutenção
            try:
                maintenance_query = """
                    SELECT COALESCE(SUM(cost), 0) as maintenance_cost
                    FROM maintenance_records
                    WHERE vehicle_id = ?
                """
                maintenance_result = db_manager.execute_query(maintenance_query, (vehicle_id,))
                maintenance_cost = maintenance_result[0]['maintenance_cost'] if maintenance_result else 0
                print(f"   ✅ Manutenção: R$ {maintenance_cost:,.2f}")
                
            except Exception as e:
                print(f"   ❌ Erro na query de manutenção: {str(e)}")
                
                # Tentar com nome de tabela alternativo
                try:
                    alt_maintenance_query = """
                        SELECT COALESCE(SUM(cost), 0) as maintenance_cost
                        FROM vehicle_maintenance
                        WHERE vehicle_id = ?
                    """
                    alt_maintenance_result = db_manager.execute_query(alt_maintenance_query, (vehicle_id,))
                    alt_maintenance_cost = alt_maintenance_result[0]['maintenance_cost'] if alt_maintenance_result else 0
                    print(f"   ✅ Manutenção (tabela alternativa): R$ {alt_maintenance_cost:,.2f}")
                    
                except Exception as e2:
                    print(f"   ❌ Erro na query de manutenção alternativa: {str(e2)}")
            
            # Contar manutenções
            try:
                maintenance_count_query = """
                    SELECT COUNT(*) as count
                    FROM maintenance_records
                    WHERE vehicle_id = ?
                """
                maintenance_count_result = db_manager.execute_query(maintenance_count_query, (vehicle_id,))
                maintenance_count = maintenance_count_result[0]['count'] if maintenance_count_result else 0
                print(f"   ✅ Registros de manutenção: {maintenance_count}")
                
            except Exception as e:
                print(f"   ❌ Erro ao contar manutenções: {str(e)}")
                
                # Tentar com tabela alternativa
                try:
                    alt_count_query = """
                        SELECT COUNT(*) as count
                        FROM vehicle_maintenance
                        WHERE vehicle_id = ?
                    """
                    alt_count_result = db_manager.execute_query(alt_count_query, (vehicle_id,))
                    alt_count = alt_count_result[0]['count'] if alt_count_result else 0
                    print(f"   ✅ Registros de manutenção (tabela alternativa): {count}")
                    
                except Exception as e2:
                    print(f"   ❌ Erro ao contar manutenções alternativas: {str(e2)}")
        
        # 4. Testar função do relatório
        print(f"\n🖥️  TESTANDO FUNÇÃO DO RELATÓRIO...")
        
        try:
            import tkinter as tk
            from tkinter import ttk
            
            # Criar janela de teste
            root = tk.Tk()
            root.withdraw()  # Esconder janela principal
            
            # Importar MainWindow
            from gui.main_window import MainWindow
            
            # Função de logout mock
            def mock_logout():
                pass
            
            # Criar MainWindow
            main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout, None)
            
            # Testar função show_vehicle_report
            main_window.show_vehicle_report()
            
            print(f"   ✅ Função show_vehicle_report executada sem erro!")
            print(f"   💡 Uma janela de relatório deve ter aparecido")
            
            # Aguardar um pouco e fechar
            root.after(3000, root.quit)
            root.mainloop()
            
        except Exception as e:
            print(f"   ❌ Erro ao testar função do relatório: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print(f"\n✅ DIAGNÓSTICO CONCLUÍDO!")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnostico_relatorio_veiculos()
