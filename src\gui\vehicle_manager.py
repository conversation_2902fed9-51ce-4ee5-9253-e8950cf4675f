#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gerenciador principal de veículos, manutenção e combustível
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from .vehicle_form import VehicleForm
from .maintenance_form import MaintenanceForm
from .fuel_form import FuelForm

class VehicleManager:
    def __init__(self, parent, db_manager, user_id):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        
        # Criar janela
        self.window = tk.Toplevel(parent)
        self.window.title("Gerenciamento de Veículos")
        self.window.geometry("1000x700")
        self.window.transient(parent)
        self.window.grab_set()
        
        # Centralizar janela
        self.center_window()
        
        # Criar interface
        self.create_widgets()
        
        # Carregar dados iniciais
        self.load_vehicles()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"1000x700+{x}+{y}")
    
    def create_widgets(self):
        """Cria os widgets da interface"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title = ttk.Label(main_frame, text="Gerenciamento de Veículos", 
                         font=('Arial', 16, 'bold'))
        title.pack(pady=(0, 20))
        
        # Notebook para abas
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Aba de veículos
        self.create_vehicles_tab()
        
        # Aba de manutenção
        self.create_maintenance_tab()
        
        # Aba de combustível
        self.create_fuel_tab()
        
        # Frame para botões
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="Fechar", command=self.close).pack(side=tk.RIGHT)
    
    def create_vehicles_tab(self):
        """Cria a aba de gerenciamento de veículos"""
        vehicles_frame = ttk.Frame(self.notebook)
        self.notebook.add(vehicles_frame, text="Veículos")
        
        # Frame para botões da aba veículos
        vehicles_buttons_frame = ttk.Frame(vehicles_frame)
        vehicles_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(vehicles_buttons_frame, text="Novo Veículo",
                  command=self.new_vehicle).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(vehicles_buttons_frame, text="Editar",
                  command=self.edit_vehicle).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(vehicles_buttons_frame, text="Excluir",
                  command=self.delete_vehicle).pack(side=tk.LEFT)
        
        # Treeview para veículos
        columns = ('ID', 'Nome', 'Marca', 'Modelo', 'Ano', 'Placa', 'Combustível', 'Quilometragem')
        self.vehicles_tree = ttk.Treeview(vehicles_frame, columns=columns, show='headings', height=15)
        
        # Configurar colunas
        for col in columns:
            self.vehicles_tree.heading(col, text=col)
            if col == 'ID':
                self.vehicles_tree.column(col, width=50, minwidth=50)
            elif col in ['Nome', 'Marca', 'Modelo']:
                self.vehicles_tree.column(col, width=120, minwidth=100)
            elif col == 'Ano':
                self.vehicles_tree.column(col, width=60, minwidth=60)
            elif col == 'Placa':
                self.vehicles_tree.column(col, width=80, minwidth=80)
            elif col == 'Combustível':
                self.vehicles_tree.column(col, width=100, minwidth=80)
            else:
                self.vehicles_tree.column(col, width=100, minwidth=80)
        
        # Scrollbar para veículos
        vehicles_scrollbar = ttk.Scrollbar(vehicles_frame, orient=tk.VERTICAL, command=self.vehicles_tree.yview)
        self.vehicles_tree.configure(yscrollcommand=vehicles_scrollbar.set)
        
        # Pack treeview e scrollbar
        self.vehicles_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        vehicles_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_maintenance_tab(self):
        """Cria a aba de manutenção"""
        maintenance_frame = ttk.Frame(self.notebook)
        self.notebook.add(maintenance_frame, text="Manutenção")
        
        # Frame para filtros e botões
        maintenance_top_frame = ttk.Frame(maintenance_frame)
        maintenance_top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Filtro por veículo
        ttk.Label(maintenance_top_frame, text="Veículo:").pack(side=tk.LEFT, padx=(0, 5))
        self.maintenance_vehicle_var = tk.StringVar()
        self.maintenance_vehicle_combo = ttk.Combobox(maintenance_top_frame, 
                                                     textvariable=self.maintenance_vehicle_var,
                                                     width=30, state='readonly')
        self.maintenance_vehicle_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.maintenance_vehicle_combo.bind('<<ComboboxSelected>>', self.filter_maintenance)
        
        # Botões
        ttk.Button(maintenance_top_frame, text="Nova Manutenção",
                  command=self.new_maintenance).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(maintenance_top_frame, text="Editar",
                  command=self.edit_maintenance).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(maintenance_top_frame, text="Excluir",
                  command=self.delete_maintenance).pack(side=tk.LEFT)
        
        # Treeview para manutenção
        maintenance_columns = ('ID', 'Veículo', 'Tipo', 'Descrição', 'Data', 'Custo', 'Prestador')
        self.maintenance_tree = ttk.Treeview(maintenance_frame, columns=maintenance_columns, 
                                           show='headings', height=15)
        
        # Configurar colunas de manutenção
        for col in maintenance_columns:
            self.maintenance_tree.heading(col, text=col)
            if col == 'ID':
                self.maintenance_tree.column(col, width=50, minwidth=50)
            elif col in ['Veículo', 'Prestador']:
                self.maintenance_tree.column(col, width=120, minwidth=100)
            elif col == 'Tipo':
                self.maintenance_tree.column(col, width=100, minwidth=80)
            elif col == 'Descrição':
                self.maintenance_tree.column(col, width=200, minwidth=150)
            elif col == 'Data':
                self.maintenance_tree.column(col, width=80, minwidth=80)
            else:
                self.maintenance_tree.column(col, width=80, minwidth=70)
        
        # Scrollbar para manutenção
        maintenance_scrollbar = ttk.Scrollbar(maintenance_frame, orient=tk.VERTICAL, 
                                            command=self.maintenance_tree.yview)
        self.maintenance_tree.configure(yscrollcommand=maintenance_scrollbar.set)
        
        # Pack treeview e scrollbar de manutenção
        self.maintenance_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        maintenance_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_fuel_tab(self):
        """Cria a aba de combustível"""
        fuel_frame = ttk.Frame(self.notebook)
        self.notebook.add(fuel_frame, text="Combustível")
        
        # Frame para filtros e botões
        fuel_top_frame = ttk.Frame(fuel_frame)
        fuel_top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Filtro por veículo
        ttk.Label(fuel_top_frame, text="Veículo:").pack(side=tk.LEFT, padx=(0, 5))
        self.fuel_vehicle_var = tk.StringVar()
        self.fuel_vehicle_combo = ttk.Combobox(fuel_top_frame, 
                                              textvariable=self.fuel_vehicle_var,
                                              width=30, state='readonly')
        self.fuel_vehicle_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.fuel_vehicle_combo.bind('<<ComboboxSelected>>', self.filter_fuel)
        
        # Botões
        ttk.Button(fuel_top_frame, text="Novo Abastecimento",
                  command=self.new_fuel_record).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(fuel_top_frame, text="Editar",
                  command=self.edit_fuel_record).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(fuel_top_frame, text="Excluir",
                  command=self.delete_fuel_record).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(fuel_top_frame, text="Estatísticas",
                  command=self.show_fuel_statistics).pack(side=tk.LEFT)
        
        # Treeview para combustível
        fuel_columns = ('ID', 'Veículo', 'Data', 'Combustível', 'Litros', 'Preço/L', 'Total', 'Km', 'Eficiência')
        self.fuel_tree = ttk.Treeview(fuel_frame, columns=fuel_columns, show='headings', height=15)
        
        # Configurar colunas de combustível
        for col in fuel_columns:
            self.fuel_tree.heading(col, text=col)
            if col == 'ID':
                self.fuel_tree.column(col, width=50, minwidth=50)
            elif col == 'Veículo':
                self.fuel_tree.column(col, width=120, minwidth=100)
            elif col in ['Data', 'Combustível']:
                self.fuel_tree.column(col, width=80, minwidth=70)
            elif col in ['Litros', 'Preço/L', 'Total', 'Km', 'Eficiência']:
                self.fuel_tree.column(col, width=70, minwidth=60)
        
        # Scrollbar para combustível
        fuel_scrollbar = ttk.Scrollbar(fuel_frame, orient=tk.VERTICAL, command=self.fuel_tree.yview)
        self.fuel_tree.configure(yscrollcommand=fuel_scrollbar.set)
        
        # Pack treeview e scrollbar de combustível
        self.fuel_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        fuel_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def load_vehicles(self):
        """Carrega a lista de veículos"""
        try:
            # Limpar treeview
            for item in self.vehicles_tree.get_children():
                self.vehicles_tree.delete(item)
            
            # Carregar veículos
            vehicles = self.db_manager.get_user_vehicles(self.user_id)
            
            # Atualizar combos de filtro
            vehicle_options = ["Todos"] + [f"{v['name']} - {v['brand']} {v['model']}" for v in vehicles]
            self.maintenance_vehicle_combo['values'] = vehicle_options
            self.fuel_vehicle_combo['values'] = vehicle_options
            
            # Definir "Todos" como padrão
            self.maintenance_vehicle_var.set("Todos")
            self.fuel_vehicle_var.set("Todos")
            
            # Preencher treeview de veículos
            for vehicle in vehicles:
                self.vehicles_tree.insert('', 'end', values=(
                    vehicle['id'],
                    vehicle['name'],
                    vehicle['brand'],
                    vehicle['model'],
                    vehicle['year'],
                    vehicle['license_plate'] or '',
                    vehicle['fuel_type'] or '',
                    vehicle['mileage'] or 0
                ))
            
            # Carregar dados das outras abas
            self.load_maintenance()
            self.load_fuel_records()
            
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar veículos: {str(e)}")
    
    def load_maintenance(self):
        """Carrega registros de manutenção"""
        try:
            # Limpar treeview
            for item in self.maintenance_tree.get_children():
                self.maintenance_tree.delete(item)
            
            # Carregar todos os registros de manutenção do usuário
            vehicles = self.db_manager.get_user_vehicles(self.user_id)
            for vehicle in vehicles:
                maintenance_records = self.db_manager.get_vehicle_maintenance(vehicle['id'], self.user_id)
                for record in maintenance_records:
                    self.maintenance_tree.insert('', 'end', values=(
                        record['id'],
                        record['vehicle_name'],
                        record['maintenance_type'],
                        record['description'][:50] + '...' if len(record['description']) > 50 else record['description'],
                        record['service_date'],
                        f"R$ {record['cost']:.2f}",
                        record['service_provider'] or ''
                    ))
                    
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar manutenção: {str(e)}")
    
    def load_fuel_records(self):
        """Carrega registros de combustível"""
        try:
            # Limpar treeview
            for item in self.fuel_tree.get_children():
                self.fuel_tree.delete(item)

            # Carregar todos os registros de combustível do usuário
            vehicles = self.db_manager.get_user_vehicles(self.user_id)

            for vehicle in vehicles:
                fuel_records = self.db_manager.get_vehicle_fuel_records(vehicle['id'], self.user_id, limit=50)

                for record in fuel_records:
                    # Calcular eficiência para exibição
                    efficiency_raw = record['fuel_efficiency'] if 'fuel_efficiency' in record else None
                    efficiency = f"{efficiency_raw:.2f}" if efficiency_raw is not None else ""

                    # Obter campos necessários com valores padrão
                    vehicle_name = record['vehicle_name'] if 'vehicle_name' in record else 'N/A'
                    fuel_date = record['fuel_date'] if 'fuel_date' in record else 'N/A'
                    fuel_type = record['fuel_type'] if 'fuel_type' in record else 'N/A'
                    liters = record['liters'] if 'liters' in record else 0
                    price_per_liter = record['price_per_liter'] if 'price_per_liter' in record else 0
                    total_cost = record['total_cost'] if 'total_cost' in record else 0
                    mileage = record['mileage'] if 'mileage' in record else ''

                    values = (
                        record['id'],
                        vehicle_name,
                        fuel_date,
                        fuel_type,
                        f"{liters:.2f}",
                        f"R$ {price_per_liter:.3f}",
                        f"R$ {total_cost:.2f}",
                        mileage or '',
                        efficiency
                    )

                    self.fuel_tree.insert('', 'end', values=values)

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar combustível: {str(e)}")
    
    def new_vehicle(self):
        """Abre formulário para novo veículo"""
        VehicleForm(self.window, self.db_manager, self.user_id, callback=self.load_vehicles)
    
    def edit_vehicle(self):
        """Edita veículo selecionado"""
        selection = self.vehicles_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um veículo para editar!")
            return

        try:
            item = self.vehicles_tree.item(selection[0])
            vehicle_id = int(item['values'][0])  # Garantir que é inteiro

            # Buscar dados completos do veículo
            vehicles = self.db_manager.get_user_vehicles(self.user_id, active_only=False)
            vehicle_data = next((v for v in vehicles if v['id'] == vehicle_id), None)

            if vehicle_data:
                # Importar VehicleForm aqui para evitar problemas de importação circular
                from .vehicle_form import VehicleForm
                VehicleForm(self.window, self.db_manager, self.user_id,
                           vehicle_data=vehicle_data, callback=self.load_vehicles)
            else:
                messagebox.showerror("Erro", f"Veículo com ID {vehicle_id} não encontrado!")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar dados do veículo: {str(e)}")
    


    def delete_vehicle(self):
        """Exclui veículo selecionado"""
        selection = self.vehicles_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um veículo para excluir!")
            return

        item = self.vehicles_tree.item(selection[0])
        vehicle_id = item['values'][0]
        vehicle_name = item['values'][1]

        if messagebox.askyesno("Confirmar", f"Deseja realmente excluir o veículo '{vehicle_name}'?"):
            try:
                self.db_manager.delete_vehicle(vehicle_id)
                messagebox.showinfo("Sucesso", "Veículo excluído com sucesso!")
                self.load_vehicles()
            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao excluir veículo: {str(e)}")
    
    def new_maintenance(self):
        """Abre formulário para nova manutenção"""
        MaintenanceForm(self.window, self.db_manager, self.user_id, callback=self.load_maintenance)
    
    def edit_maintenance(self):
        """Edita manutenção selecionada"""
        selection = self.maintenance_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma manutenção para editar!")
            return

        try:
            item = self.maintenance_tree.item(selection[0])
            maintenance_id = int(item['values'][0])  # ID da manutenção

            # Buscar dados completos da manutenção
            maintenance_query = """
                SELECT vm.*, v.name as vehicle_name
                FROM vehicle_maintenance vm
                JOIN vehicles v ON vm.vehicle_id = v.id
                WHERE vm.id = ? AND vm.user_id = ?
            """
            maintenance_records = self.db_manager.execute_query(maintenance_query, (maintenance_id, self.user_id))

            if maintenance_records:
                maintenance_data = maintenance_records[0]

                # Importar MaintenanceForm aqui para evitar problemas de importação
                from .maintenance_form import MaintenanceForm
                MaintenanceForm(self.window, self.db_manager, self.user_id,
                               maintenance_data=maintenance_data, callback=self.load_maintenance)
            else:
                messagebox.showerror("Erro", f"Registro de manutenção com ID {maintenance_id} não encontrado!")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar dados da manutenção: {str(e)}")

    def delete_maintenance(self):
        """Exclui manutenção selecionada"""
        selection = self.maintenance_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma manutenção para excluir!")
            return

        item = self.maintenance_tree.item(selection[0])
        maintenance_id = item['values'][0]
        vehicle_name = item['values'][1]
        maintenance_type = item['values'][2]
        service_date = item['values'][4]

        if messagebox.askyesno("Confirmar",
                              f"Deseja realmente excluir o registro de manutenção?\n\n" +
                              f"Veículo: {vehicle_name}\n" +
                              f"Tipo: {maintenance_type}\n" +
                              f"Data: {service_date}"):
            try:
                self.db_manager.delete_maintenance_record(maintenance_id)
                messagebox.showinfo("Sucesso", "Registro de manutenção excluído com sucesso!")
                self.load_maintenance()
            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao excluir registro: {str(e)}")
    
    def new_fuel_record(self):
        """Abre formulário para novo abastecimento"""
        FuelForm(self.window, self.db_manager, self.user_id, callback=self.load_fuel_records)
    
    def edit_fuel_record(self):
        """Edita registro de combustível selecionado"""
        selection = self.fuel_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um abastecimento para editar!")
            return

        try:
            item = self.fuel_tree.item(selection[0])
            fuel_record_id = int(item['values'][0])  # ID do registro de combustível

            # Buscar dados completos do registro de combustível
            fuel_query = """
                SELECT fr.*, v.name as vehicle_name
                FROM fuel_records fr
                JOIN vehicles v ON fr.vehicle_id = v.id
                WHERE fr.id = ? AND fr.user_id = ?
            """
            fuel_records = self.db_manager.execute_query(fuel_query, (fuel_record_id, self.user_id))

            if fuel_records:
                fuel_data = fuel_records[0]

                # Importar FuelForm aqui para evitar problemas de importação
                from .fuel_form import FuelForm
                FuelForm(self.window, self.db_manager, self.user_id,
                        fuel_data=fuel_data, callback=self.load_fuel_records)
            else:
                messagebox.showerror("Erro", f"Registro de combustível com ID {fuel_record_id} não encontrado!")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar dados do combustível: {str(e)}")

    def delete_fuel_record(self):
        """Exclui registro de combustível selecionado"""
        selection = self.fuel_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um abastecimento para excluir!")
            return

        item = self.fuel_tree.item(selection[0])
        record_id = item['values'][0]
        vehicle_name = item['values'][1]
        fuel_date = item['values'][2]

        if messagebox.askyesno("Confirmar",
                              f"Deseja realmente excluir o registro de abastecimento?\n\n" +
                              f"Veículo: {vehicle_name}\n" +
                              f"Data: {fuel_date}"):
            try:
                self.db_manager.delete_fuel_record(record_id)
                messagebox.showinfo("Sucesso", "Registro de combustível excluído com sucesso!")
                self.load_fuel_records()
            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao excluir registro: {str(e)}")

    def show_fuel_statistics(self):
        """Mostra estatísticas de combustível"""
        try:
            # Criar janela de estatísticas
            stats_window = tk.Toplevel(self.window)
            stats_window.title("Estatísticas de Combustível")
            stats_window.geometry("600x500")
            stats_window.transient(self.window)
            stats_window.grab_set()

            # Centralizar janela
            stats_window.update_idletasks()
            x = (stats_window.winfo_screenwidth() // 2) - (600 // 2)
            y = (stats_window.winfo_screenheight() // 2) - (500 // 2)
            stats_window.geometry(f"600x500+{x}+{y}")

            # Frame principal
            main_frame = ttk.Frame(stats_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Título
            ttk.Label(main_frame, text="📊 Estatísticas de Combustível",
                     font=('Arial', 16, 'bold')).pack(pady=(0, 20))

            # Buscar estatísticas
            vehicles = self.db_manager.get_user_vehicles(self.user_id)

            if not vehicles:
                ttk.Label(main_frame, text="Nenhum veículo cadastrado.",
                         font=('Arial', 12)).pack(pady=20)
                return

            # Frame para estatísticas
            stats_frame = ttk.Frame(main_frame)
            stats_frame.pack(fill=tk.BOTH, expand=True)

            # Calcular estatísticas para cada veículo
            for i, vehicle in enumerate(vehicles):
                # Frame do veículo
                vehicle_frame = ttk.LabelFrame(stats_frame,
                                             text=f"{vehicle['name']} - {vehicle['brand']} {vehicle['model']}",
                                             padding="10")
                vehicle_frame.pack(fill=tk.X, pady=(0, 10))

                # Buscar registros de combustível
                fuel_records = self.db_manager.get_vehicle_fuel_records(vehicle['id'], self.user_id)

                if not fuel_records:
                    ttk.Label(vehicle_frame, text="Nenhum registro de combustível encontrado.").pack()
                    continue

                # Calcular estatísticas
                total_liters = sum(record['liters'] for record in fuel_records)
                total_cost = sum(record['total_cost'] for record in fuel_records)
                avg_price_per_liter = total_cost / total_liters if total_liters > 0 else 0

                # Eficiência média (apenas registros com eficiência calculada)
                efficiency_records = [record for record in fuel_records if record.get('efficiency')]
                avg_efficiency = sum(float(record['efficiency']) for record in efficiency_records) / len(efficiency_records) if efficiency_records else 0

                # Exibir estatísticas
                stats_text = f"""
Total de Abastecimentos: {len(fuel_records)}
Total de Litros: {total_liters:.2f} L
Custo Total: R$ {total_cost:.2f}
Preço Médio por Litro: R$ {avg_price_per_liter:.3f}
Eficiência Média: {avg_efficiency:.2f} km/L
                """.strip()

                ttk.Label(vehicle_frame, text=stats_text, font=('Arial', 10)).pack(anchor=tk.W)

            # Botão fechar
            ttk.Button(main_frame, text="Fechar",
                      command=stats_window.destroy).pack(pady=(20, 0))

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar estatísticas: {str(e)}")
    
    def filter_maintenance(self, event=None):
        """Filtra manutenção por veículo"""
        # Implementar filtro
        pass
    
    def filter_fuel(self, event=None):
        """Filtra combustível por veículo"""
        # Implementar filtro
        pass
    
    def close(self):
        """Fecha a janela"""
        self.window.destroy()
