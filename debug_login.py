#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug da tela de login
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def debug_sistema():
    """Debug completo do sistema"""
    print("🔍 DEBUG DO SISTEMA DE LOGIN")
    print("=" * 40)
    
    # 1. Testar tkinter básico
    print("\n1️⃣ TESTANDO TKINTER BÁSICO...")
    try:
        root = tk.Tk()
        print(f"   ✅ Tkinter versão: {tk.TkVersion}")
        print(f"   ✅ Tcl versão: {tk.TclVersion}")
        
        # Informações da tela
        width = root.winfo_screenwidth()
        height = root.winfo_screenheight()
        print(f"   📺 Resolução da tela: {width}x{height}")
        
        root.destroy()
        print("   ✅ Tkinter funcionando!")
        
    except Exception as e:
        print(f"   ❌ Erro no tkinter: {str(e)}")
        return False
    
    # 2. Testar janela simples
    print("\n2️⃣ TESTANDO JANELA SIMPLES...")
    try:
        root = tk.Tk()
        root.title("Teste Debug")
        root.geometry("300x200+100+100")  # Posição fixa
        root.configure(bg="red")  # Cor chamativa
        
        # Informações da janela
        print(f"   📍 Posição: x={root.winfo_x()}, y={root.winfo_y()}")
        print(f"   📏 Tamanho: {root.winfo_width()}x{root.winfo_height()}")
        
        # Forçar visibilidade
        root.lift()
        root.attributes('-topmost', True)
        root.focus_force()
        
        # Label chamativo
        label = tk.Label(root, text="🚨 JANELA DE TESTE 🚨", 
                        font=('Arial', 14, 'bold'),
                        bg="red", fg="white")
        label.pack(expand=True)
        
        # Botão para fechar
        btn = tk.Button(root, text="FECHAR", 
                       command=root.quit,
                       font=('Arial', 12, 'bold'),
                       bg="white", fg="red")
        btn.pack(pady=10)
        
        print("   🚨 JANELA VERMELHA CRIADA!")
        print("   👀 Procure uma janela VERMELHA na sua tela")
        print("   ⏰ Fechará automaticamente em 10 segundos")
        
        # Auto-fechar em 10 segundos
        root.after(10000, root.quit)
        
        # Executar
        root.mainloop()
        root.destroy()
        
        print("   ✅ Janela simples funcionou!")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro na janela simples: {str(e)}")
        return False

def debug_login_completo():
    """Debug do login completo"""
    print("\n3️⃣ TESTANDO LOGIN COMPLETO...")
    
    try:
        # Adicionar src ao path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from database import DatabaseManager
        from auth import AuthManager
        
        # Inicializar
        print("   📦 Inicializando banco...")
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        print("   ✅ Sistema inicializado!")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Debug Login")
        root.withdraw()  # Esconder principal
        
        # Importar LoginWindow
        from gui.login_window import LoginWindow
        
        def callback(user_data):
            print(f"   ✅ LOGIN REALIZADO: {user_data['full_name']}")
            messagebox.showinfo("Sucesso!", f"Login: {user_data['full_name']}")
            root.quit()
        
        # Criar login com layout clássico (mais compatível)
        print("   🔑 Criando janela de login...")
        login_window = LoginWindow(root, auth_manager, callback, "classic")
        
        print("   👀 Procure a janela de login na sua tela")
        print("   🔑 Credenciais: admin / admin123")
        
        # Executar
        root.mainloop()
        root.destroy()
        
        print("   ✅ Login completo testado!")
        
    except Exception as e:
        print(f"   ❌ Erro no login completo: {str(e)}")
        import traceback
        traceback.print_exc()

def menu_debug():
    """Menu de opções de debug"""
    while True:
        print("\n🔍 MENU DE DEBUG")
        print("=" * 25)
        print("1. 🧪 Testar tkinter básico")
        print("2. 🚨 Testar janela simples (vermelha)")
        print("3. 🔑 Testar login completo")
        print("4. 🔄 Executar todos os testes")
        print("0. ❌ Sair")
        print()
        
        choice = input("Escolha uma opção (0-4): ").strip()
        
        if choice == "0":
            print("👋 Saindo do debug...")
            break
        elif choice == "1":
            debug_sistema()
        elif choice == "2":
            print("\n2️⃣ TESTANDO JANELA SIMPLES...")
            try:
                root = tk.Tk()
                root.title("🚨 TESTE DEBUG 🚨")
                root.geometry("400x300+200+200")
                root.configure(bg="red")
                
                root.lift()
                root.attributes('-topmost', True)
                root.focus_force()
                
                tk.Label(root, text="🚨 VOCÊ VÊ ESTA JANELA? 🚨", 
                        font=('Arial', 16, 'bold'),
                        bg="red", fg="white").pack(expand=True)
                
                tk.Button(root, text="SIM, VEJ0!", 
                         command=root.quit,
                         font=('Arial', 14, 'bold'),
                         bg="green", fg="white").pack(pady=10)
                
                print("🚨 JANELA VERMELHA CRIADA!")
                print("👀 Você deve ver uma janela VERMELHA")
                
                root.after(15000, root.quit)
                root.mainloop()
                root.destroy()
                
            except Exception as e:
                print(f"❌ Erro: {str(e)}")
                
        elif choice == "3":
            debug_login_completo()
        elif choice == "4":
            print("\n🔄 EXECUTANDO TODOS OS TESTES...")
            if debug_sistema():
                debug_login_completo()
        else:
            print("❌ Opção inválida!")

if __name__ == "__main__":
    menu_debug()
