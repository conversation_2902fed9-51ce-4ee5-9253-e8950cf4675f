#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de gestão financeira - Versão simplificada
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox, ttk

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def sistema_simples():
    """Sistema simplificado com login integrado"""
    print("💰 SISTEMA DE GESTÃO FINANCEIRA")
    print("=" * 40)
    print("🔑 Versão simplificada com login integrado")
    print()
    
    # Criar janela principal
    root = tk.Tk()
    root.title("💰 Gestão Financeira")
    root.geometry("800x600")
    root.configure(bg="#f0f0f0")
    
    # Centralizar janela
    root.eval('tk::PlaceWindow . center')
    
    # Forçar para frente
    root.lift()
    root.attributes('-topmost', True)
    root.after(1000, lambda: root.attributes('-topmost', False))
    
    # Estado do login
    logged_in = {"status": False, "user": None}

    # Variáveis globais para widgets
    login_frame = None
    user_entry = None
    pass_entry = None
    
    def fazer_login():
        """Função de login"""
        usuario = user_entry.get()
        senha = pass_entry.get()
        
        if usuario == "admin" and senha == "admin123":
            logged_in["status"] = True
            logged_in["user"] = "Administrador"
            
            # Esconder frame de login
            login_frame.pack_forget()
            
            # Mostrar sistema principal
            mostrar_sistema_principal()
            
            messagebox.showinfo("Sucesso", "Login realizado com sucesso!")
        else:
            messagebox.showerror("Erro", "Usuário ou senha incorretos!")
    
    def mostrar_sistema_principal():
        """Mostra interface principal do sistema"""
        # Frame principal
        main_frame = tk.Frame(root, bg="#f0f0f0")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Título
        title = tk.Label(main_frame, text="💰 Sistema de Gestão Financeira", 
                        font=('Arial', 20, 'bold'),
                        bg="#f0f0f0", fg="#333")
        title.pack(pady=(0, 20))
        
        # Informações do usuário
        user_info = tk.Label(main_frame, text=f"👤 Usuário: {logged_in['user']}", 
                            font=('Arial', 12),
                            bg="#f0f0f0", fg="#666")
        user_info.pack(anchor='w')
        
        # Notebook para abas
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=20)
        
        # Aba Dashboard
        dashboard_frame = tk.Frame(notebook, bg="white")
        notebook.add(dashboard_frame, text="📊 Dashboard")
        
        tk.Label(dashboard_frame, text="📊 Dashboard Financeiro", 
                font=('Arial', 16, 'bold'),
                bg="white").pack(pady=20)
        
        tk.Label(dashboard_frame, text="✅ Sistema funcionando perfeitamente!", 
                font=('Arial', 12),
                bg="white", fg="green").pack(pady=10)
        
        # Aba Transações
        trans_frame = tk.Frame(notebook, bg="white")
        notebook.add(trans_frame, text="💳 Transações")
        
        tk.Label(trans_frame, text="💳 Gerenciar Transações", 
                font=('Arial', 16, 'bold'),
                bg="white").pack(pady=20)
        
        # Aba Relatórios
        reports_frame = tk.Frame(notebook, bg="white")
        notebook.add(reports_frame, text="📈 Relatórios")
        
        tk.Label(reports_frame, text="📈 Relatórios Financeiros", 
                font=('Arial', 16, 'bold'),
                bg="white").pack(pady=20)
        
        # Botão logout
        logout_btn = tk.Button(main_frame, text="🚪 Logout", 
                              command=fazer_logout,
                              font=('Arial', 10),
                              bg="#dc3545", fg="white")
        logout_btn.pack(anchor='e', pady=10)
    
    def fazer_logout():
        """Função de logout"""
        logged_in["status"] = False
        logged_in["user"] = None
        
        # Limpar janela
        for widget in root.winfo_children():
            widget.destroy()
        
        # Recriar login
        criar_tela_login()
    
    def criar_tela_login():
        """Cria tela de login"""
        
        # Frame de login
        login_frame = tk.Frame(root, bg="#f0f0f0")
        login_frame.pack(expand=True)
        
        # Card de login
        card = tk.Frame(login_frame, bg="white", relief="solid", bd=1, padx=40, pady=40)
        card.pack()
        
        # Título
        title = tk.Label(card, text="💰 Gestão Financeira", 
                        font=('Arial', 20, 'bold'),
                        bg="white", fg="#333")
        title.pack(pady=(0, 30))
        
        # Campo usuário
        tk.Label(card, text="Usuário:", font=('Arial', 12, 'bold'),
                bg="white", fg="#555").pack(anchor='w')
        
        user_entry = tk.Entry(card, font=('Arial', 12), width=25)
        user_entry.pack(pady=(5, 15), ipady=8)
        user_entry.insert(0, "admin")  # Pré-preencher
        
        # Campo senha
        tk.Label(card, text="Senha:", font=('Arial', 12, 'bold'),
                bg="white", fg="#555").pack(anchor='w')
        
        pass_entry = tk.Entry(card, font=('Arial', 12), width=25, show='*')
        pass_entry.pack(pady=(5, 20), ipady=8)
        pass_entry.insert(0, "admin123")  # Pré-preencher
        
        # Botão login
        login_btn = tk.Button(card, text="ENTRAR", 
                             font=('Arial', 12, 'bold'),
                             bg="#28a745", fg="white", 
                             width=20, height=2,
                             command=fazer_login)
        login_btn.pack(pady=10)
        
        # Info
        tk.Label(card, text="Credenciais já preenchidas - Clique em ENTRAR", 
                font=('Arial', 9), fg="#666", bg="white").pack(pady=10)
        
        # Focar no botão
        login_btn.focus()
        
        # Bind Enter
        root.bind('<Return>', lambda e: fazer_login())
    
    # Criar tela de login inicial
    criar_tela_login()
    
    print("🖥️  Sistema iniciado!")
    print("💡 Credenciais já estão preenchidas")
    print("🔑 Clique em ENTRAR para acessar o sistema")
    print("👀 Procure a janela na sua tela")
    
    # Executar sistema
    root.mainloop()
    
    print("✅ Sistema encerrado!")

if __name__ == "__main__":
    sistema_simples()
