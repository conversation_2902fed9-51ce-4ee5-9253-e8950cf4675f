#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para configurar dados de teste
"""

import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager

def setup_test_data():
    """Configura dados básicos para teste"""
    print("🔧 CONFIGURANDO DADOS DE TESTE")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Criar usuário admin se não existir
        if not auth_manager.user_exists('admin'):
            auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
            print("✅ Usuário admin criado")
        else:
            print("✅ Usuário admin já existe")
        
        # Obter ID do admin
        admin_user = db_manager.execute_query("SELECT id FROM users WHERE username = 'admin'")[0]
        admin_id = admin_user['id']
        print(f"✅ Admin ID: {admin_id}")
        
        # Verificar se já existe carteira
        result = db_manager.execute_query("SELECT COUNT(*) as count FROM wallets WHERE user_id = ?", (admin_id,))
        existing_wallets_count = result[0]['count'] if result else 0
        if existing_wallets_count == 0:
            # Criar carteira de teste
            db_manager.execute_query("""
                INSERT INTO wallets (user_id, name, current_balance)
                VALUES (?, ?, ?)
            """, (admin_id, 'Carteira Principal', 1000.00))
            print("✅ Carteira de teste criada")
        else:
            print("✅ Carteira já existe")
        
        # Verificar dados finais
        result = db_manager.execute_query("SELECT COUNT(*) as count FROM users")
        users = result[0]['count'] if result else 0
        result = db_manager.execute_query("SELECT COUNT(*) as count FROM wallets")
        wallets = result[0]['count'] if result else 0
        result = db_manager.execute_query("SELECT COUNT(*) as count FROM categories")
        categories = result[0]['count'] if result else 0
        
        print(f"\n📊 DADOS CONFIGURADOS:")
        print(f"   Usuários: {users}")
        print(f"   Carteiras: {wallets}")
        print(f"   Categorias: {categories}")
        
        print("\n✅ CONFIGURAÇÃO CONCLUÍDA!")
        print("Agora você pode testar as transações.")
        
    except Exception as e:
        print(f"❌ ERRO: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    setup_test_data()
