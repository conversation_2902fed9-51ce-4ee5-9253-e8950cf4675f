#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formulário para gerenciamento de manutenção de veículos
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
try:
    from tkcalendar import DateEntry
    TKCALENDAR_AVAILABLE = True
except ImportError:
    TKCALENDAR_AVAILABLE = False
    print("⚠️ tkcalendar não disponível - usando Entry simples para datas")

class MaintenanceForm:


    def create_date_entry(self, parent, textvariable, width=37):
        """Cria um DateEntry simples e funcional"""

        # Sempre usar Entry simples com botão de calendário
        # Isso garante que sempre funcione, independentemente de problemas do tkcalendar

        # Frame para conter o Entry e o botão
        date_frame = ttk.Frame(parent)

        # Entry para a data
        date_entry = ttk.Entry(date_frame, textvariable=textvariable, width=width-5)
        date_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Botão para abrir calendário
        calendar_button = ttk.Button(date_frame, text="📅", width=3,
                                   command=lambda: self._open_calendar_popup(textvariable, date_entry))
        calendar_button.pack(side=tk.RIGHT, padx=(2, 0))

        # Adicionar validação em tempo real
        date_entry.bind('<KeyRelease>', lambda e: self._validate_date_input(e, textvariable))
        date_entry.bind('<FocusOut>', lambda e: self._validate_date_format(e, textvariable))

        # Adicionar tooltip
        self._add_date_tooltip(date_entry)

        # Configurar data padrão
        if not textvariable.get():
            today = date.today()
            textvariable.set(today.strftime('%d/%m/%Y'))

        return date_frame

    def _open_calendar_popup(self, textvariable, entry_widget):
        """Abre um calendário popup funcional com seleção garantida"""
        try:
            if not TKCALENDAR_AVAILABLE:
                messagebox.showinfo("Calendário",
                                  "Digite a data no formato dd/mm/aaaa\n" +
                                  "Exemplo: 18/07/2025")
                entry_widget.focus_set()
                return

            # Criar janela popup
            popup = tk.Toplevel()
            popup.title("📅 Selecionar Data")
            popup.geometry("325x360")
            popup.resizable(False, False)
            popup.transient(entry_widget.winfo_toplevel())
            popup.grab_set()

            # Centralizar popup
            popup.update_idletasks()
            x = entry_widget.winfo_rootx() + 50
            y = entry_widget.winfo_rooty() + 30
            popup.geometry(f"+{x}+{y}")

            # Frame principal
            main_frame = ttk.Frame(popup, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Título
            ttk.Label(main_frame, text="📅 Selecione uma data",
                     font=('Arial', 12, 'bold')).pack(pady=(0, 10))

            # Data atual do campo ou hoje
            current_date = date.today()
            current_text = textvariable.get().strip()
            if current_text and len(current_text) == 10:
                try:
                    day, month, year = current_text.split('/')
                    current_date = date(int(year), int(month), int(day))
                except:
                    pass

            # Calendário com configurações simplificadas
            from tkcalendar import Calendar

            calendar = Calendar(main_frame,
                              selectmode='day',
                              date_pattern='dd/mm/yyyy',
                              background='white',
                              foreground='black',
                              headersbackground='#2E86AB',
                              headersforeground='white',
                              selectbackground='#F18F01',
                              selectforeground='white',
                              normalbackground='white',
                              normalforeground='black',
                              weekendbackground='#E8F4FD',
                              weekendforeground='#2E86AB',
                              showweeknumbers=False,
                              firstweekday='monday',
                              font=('Arial', 10))
            calendar.pack(pady=(0, 15))

            # Definir data inicial
            calendar.selection_set(current_date)

            # Label para mostrar data selecionada
            selected_label = ttk.Label(main_frame,
                                     text=f"Selecionada: {current_date.strftime('%d/%m/%Y')}",
                                     font=('Arial', 10, 'bold'),
                                     foreground='#2E86AB')
            selected_label.pack(pady=(0, 10))

            # Função SIMPLES para capturar seleção
            def update_selection():
                try:
                    selected = calendar.get_date()
                    selected_label.config(text=f"Selecionada: {selected}")
                    return selected
                except:
                    return current_date.strftime('%d/%m/%Y')

            # Atualizar seleção periodicamente
            def auto_update():
                update_selection()
                popup.after(200, auto_update)

            popup.after(100, auto_update)

            # Frame para botões
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)

            # Função para confirmar seleção
            def confirm_selection():
                try:
                    selected_date = calendar.get_date()
                    textvariable.set(selected_date)
                    print(f"✅ Data selecionada: {selected_date}")
                    popup.destroy()
                except Exception as e:
                    print(f"Erro: {e}")
                    # Fallback: usar data do label
                    label_text = selected_label.cget('text')
                    if 'Selecionada: ' in label_text:
                        date_part = label_text.replace('Selecionada: ', '')
                        textvariable.set(date_part)
                        popup.destroy()

            # Função para hoje
            def select_today():
                today = date.today()
                calendar.selection_set(today)
                textvariable.set(today.strftime('%d/%m/%Y'))
                print(f"✅ Data de hoje selecionada: {today.strftime('%d/%m/%Y')}")
                popup.destroy()

            # Função para cancelar
            def cancel_selection():
                popup.destroy()

            # Botões
            ttk.Button(button_frame, text="📅 Hoje",
                      command=select_today).pack(side=tk.LEFT, padx=(0, 10))

            ttk.Button(button_frame, text="❌ Cancelar",
                      command=cancel_selection).pack(side=tk.RIGHT)

            ttk.Button(button_frame, text="✅ Confirmar",
                      command=confirm_selection).pack(side=tk.RIGHT, padx=(0, 10))

            # Eventos do calendário
            calendar.bind("<Button-1>", lambda e: popup.after(100, update_selection))
            calendar.bind("<Double-Button-1>", lambda e: popup.after(100, confirm_selection))
            calendar.bind("<<CalendarSelected>>", lambda e: update_selection())

            # Instruções
            ttk.Label(main_frame,
                     text="💡 Clique em um dia e depois em 'Confirmar'\n💡 Ou duplo-clique em um dia",
                     font=('Arial', 8),
                     foreground='gray',
                     justify='center').pack(pady=(10, 0))

            # Focar no calendário
            calendar.focus_set()

        except Exception as e:
            print(f"Erro ao abrir calendário popup: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showinfo("Calendário",
                              "Digite a data no formato dd/mm/aaaa\n" +
                              "Exemplo: 18/07/2025")
            entry_widget.focus_set()

    def _validate_date_input(self, event, textvariable):
        """Valida entrada de data em tempo real"""
        try:
            text = textvariable.get()
            # Permitir apenas números e /
            cleaned = ''.join(c for c in text if c.isdigit() or c == '/')
            if cleaned != text:
                textvariable.set(cleaned)
        except:
            pass

    def _validate_date_format(self, event, textvariable):
        """Valida formato da data quando perde o foco"""
        try:
            text = textvariable.get().strip()
            if not text:
                return

            # Tentar formatar automaticamente
            if len(text) == 8 and '/' not in text:
                # Formato ddmmaaaa -> dd/mm/aaaa
                formatted = f"{text[:2]}/{text[2:4]}/{text[4:]}"
                textvariable.set(formatted)
                text = formatted

            # Validar formato dd/mm/aaaa
            if len(text) == 10 and text.count('/') == 2:
                parts = text.split('/')
                if len(parts) == 3:
                    day, month, year = parts
                    # Validação básica
                    day_int = int(day)
                    month_int = int(month)
                    year_int = int(year)

                    if not (1 <= day_int <= 31):
                        raise ValueError(f"Dia inválido: {day}")
                    if not (1 <= month_int <= 12):
                        raise ValueError(f"Mês inválido: {month}")
                    if not (1900 <= year_int <= 2100):
                        raise ValueError(f"Ano inválido: {year}")

                    # Validação de dias por mês
                    import calendar
                    if day_int > calendar.monthrange(year_int, month_int)[1]:
                        raise ValueError(f"Dia {day} é inválido para {month}/{year}")

                    # Se chegou aqui, data é válida
                    event.widget.configure(style='')
                    return

            # Se chegou aqui, formato está incorreto
            event.widget.configure(style='Error.TEntry')

        except ValueError as e:
            event.widget.configure(style='Error.TEntry')
        except:
            pass

    def _add_date_tooltip(self, widget):
        """Adiciona tooltip com formato de data"""
        try:
            def show_tooltip(event):
                tooltip = tk.Toplevel()
                tooltip.wm_overrideredirect(True)
                tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
                label = tk.Label(tooltip, text="Formato: dd/mm/aaaa\nEx: 18/07/2025\nClique 📅 para calendário",
                               background='#FFFFE0', foreground='black',
                               relief='solid', borderwidth=1, font=('Arial', 8))
                label.pack()
                widget.tooltip = tooltip

            def hide_tooltip(event):
                if hasattr(widget, 'tooltip'):
                    widget.tooltip.destroy()
                    del widget.tooltip

            widget.bind('<Enter>', show_tooltip)
            widget.bind('<Leave>', hide_tooltip)
        except:
            pass

    def _setup_styles(self):
        """Configura estilos para os widgets"""
        try:
            style = ttk.Style()
            # Estilo para campos com erro
            style.configure('Error.TEntry', fieldbackground='#FFE6E6', bordercolor='red')
        except:
            pass

    def __init__(self, parent, db_manager, user_id, vehicle_id=None, maintenance_data=None, callback=None):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.vehicle_id = vehicle_id
        self.maintenance_data = maintenance_data
        self.callback = callback
        self.is_edit_mode = maintenance_data is not None

        # Configurar estilos
        self._setup_styles()

        # Criar janela
        self.window = tk.Toplevel(parent)
        self.window.title("Editar Manutenção" if self.is_edit_mode else "Nova Manutenção")
        self.window.geometry("800x800")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        # Centralizar janela
        self.center_window()
        
        # Carregar veículos
        self.load_vehicles()
        
        # Criar interface
        self.create_widgets()
        
        # Preencher dados se for edição
        if self.is_edit_mode:
            self.populate_fields()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (800 // 2)
        self.window.geometry(f"500x750+{x}+{y}")
    
    def load_vehicles(self):
        """Carrega lista de veículos do usuário"""
        try:
            self.vehicles = self.db_manager.get_user_vehicles(self.user_id)
            self.vehicle_options = {f"{v['name']} - {v['brand']} {v['model']}": v['id'] for v in self.vehicles}
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar veículos: {str(e)}")
            self.vehicles = []
            self.vehicle_options = {}
    
    def create_widgets(self):
        """Cria os widgets do formulário"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title = ttk.Label(main_frame, text="Editar Manutenção" if self.is_edit_mode else "Nova Manutenção",
                         font=('Arial', 14, 'bold'))
        title.pack(pady=(0, 20))
        
        # Frame para campos
        fields_frame = ttk.Frame(main_frame)
        fields_frame.pack(fill=tk.BOTH, expand=True)
        
        # Veículo
        ttk.Label(fields_frame, text="Veículo:*").grid(row=0, column=0, sticky='w', pady=5)
        self.vehicle_var = tk.StringVar()
        vehicle_combo = ttk.Combobox(fields_frame, textvariable=self.vehicle_var, width=37, state='readonly')
        vehicle_combo['values'] = list(self.vehicle_options.keys())
        vehicle_combo.grid(row=0, column=1, sticky='ew', pady=5)
        
        # Pré-selecionar veículo se fornecido
        if self.vehicle_id:
            for name, vid in self.vehicle_options.items():
                if vid == self.vehicle_id:
                    self.vehicle_var.set(name)
                    break
        
        # Tipo de manutenção
        ttk.Label(fields_frame, text="Tipo de Manutenção:*").grid(row=1, column=0, sticky='w', pady=5)
        self.maintenance_type_var = tk.StringVar()
        type_combo = ttk.Combobox(fields_frame, textvariable=self.maintenance_type_var, width=37, state='readonly')
        type_combo['values'] = ('oil_change', 'tire_change', 'brake_service', 'general_service', 'repair')
        type_combo.grid(row=1, column=1, sticky='ew', pady=5)
        
        # Descrição
        ttk.Label(fields_frame, text="Descrição:*").grid(row=2, column=0, sticky='nw', pady=5)
        self.description_text = tk.Text(fields_frame, height=3, width=40)
        self.description_text.grid(row=2, column=1, sticky='ew', pady=5)
        
        # Data do serviço
        ttk.Label(fields_frame, text="Data do Serviço:*").grid(row=3, column=0, sticky='w', pady=5)
        self.service_date_var = tk.StringVar()
        self.service_date_entry = self.create_date_entry(fields_frame, self.service_date_var)
        self.service_date_entry.grid(row=3, column=1, sticky='ew', pady=5)
        
        # Quilometragem no serviço
        ttk.Label(fields_frame, text="Quilometragem:").grid(row=4, column=0, sticky='w', pady=5)
        self.mileage_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.mileage_var, width=40).grid(row=4, column=1, sticky='ew', pady=5)
        
        # Custo
        ttk.Label(fields_frame, text="Custo:*").grid(row=5, column=0, sticky='w', pady=5)
        self.cost_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.cost_var, width=40).grid(row=5, column=1, sticky='ew', pady=5)
        
        # Prestador de serviço
        ttk.Label(fields_frame, text="Prestador de Serviço:").grid(row=6, column=0, sticky='w', pady=5)
        self.service_provider_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.service_provider_var, width=40).grid(row=6, column=1, sticky='ew', pady=5)
        
        # Próxima manutenção - data
        ttk.Label(fields_frame, text="Próxima Manutenção:").grid(row=7, column=0, sticky='w', pady=5)
        self.next_service_date_var = tk.StringVar()
        self.next_service_date_entry = self.create_date_entry(fields_frame, self.next_service_date_var)
        self.next_service_date_entry.grid(row=7, column=1, sticky='ew', pady=5)
        
        # Próxima manutenção - quilometragem
        ttk.Label(fields_frame, text="Próxima Km:").grid(row=8, column=0, sticky='w', pady=5)
        self.next_mileage_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.next_mileage_var, width=40).grid(row=8, column=1, sticky='ew', pady=5)
        
        # Período de garantia (meses)
        ttk.Label(fields_frame, text="Garantia (meses):").grid(row=9, column=0, sticky='w', pady=5)
        self.warranty_period_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.warranty_period_var, width=40).grid(row=9, column=1, sticky='ew', pady=5)
        
        # Data de vencimento da garantia
        ttk.Label(fields_frame, text="Vencimento Garantia:").grid(row=10, column=0, sticky='w', pady=5)
        self.warranty_expiry_var = tk.StringVar()
        self.warranty_expiry_entry = self.create_date_entry(fields_frame, self.warranty_expiry_var)
        self.warranty_expiry_entry.grid(row=10, column=1, sticky='ew', pady=5)
        
        # Número do recibo
        ttk.Label(fields_frame, text="Nº Recibo:").grid(row=11, column=0, sticky='w', pady=5)
        self.receipt_number_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.receipt_number_var, width=40).grid(row=11, column=1, sticky='ew', pady=5)
        
        # Checkboxes
        checkbox_frame = ttk.Frame(fields_frame)
        checkbox_frame.grid(row=12, column=1, sticky='w', pady=10)
        
        self.is_scheduled_var = tk.BooleanVar()
        ttk.Checkbutton(checkbox_frame, text="Manutenção Agendada", 
                       variable=self.is_scheduled_var).pack(anchor='w')
        
        self.is_completed_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(checkbox_frame, text="Manutenção Concluída", 
                       variable=self.is_completed_var).pack(anchor='w')
        
        # Observações
        ttk.Label(fields_frame, text="Observações:").grid(row=13, column=0, sticky='nw', pady=5)
        self.notes_text = tk.Text(fields_frame, height=4, width=40)
        self.notes_text.grid(row=13, column=1, sticky='ew', pady=5)
        
        # Configurar grid
        fields_frame.columnconfigure(1, weight=1)
        
        # Frame para botões
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Botões
        ttk.Button(buttons_frame, text="Cancelar", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Salvar", command=self.save).pack(side=tk.RIGHT)
    
    def populate_fields(self):
        """Preenche os campos com dados da manutenção para edição"""
        if not self.maintenance_data:
            return
        
        # Selecionar veículo
        for name, vid in self.vehicle_options.items():
            if vid == self.maintenance_data['vehicle_id']:
                self.vehicle_var.set(name)
                break

        self.maintenance_type_var.set(self.maintenance_data['maintenance_type'] or '')

        if self.maintenance_data['description']:
            self.description_text.insert('1.0', self.maintenance_data['description'])

        if self.maintenance_data['service_date']:
            self.service_date_var.set(self.maintenance_data['service_date'])

        if self.maintenance_data['mileage_at_service']:
            self.mileage_var.set(str(self.maintenance_data['mileage_at_service']))
        
        if self.maintenance_data.get('cost'):
            self.cost_var.set(str(self.maintenance_data['cost']))
        
        self.service_provider_var.set(self.maintenance_data.get('service_provider', ''))
        
        if self.maintenance_data.get('next_service_date'):
            self.next_service_date_var.set(self.maintenance_data['next_service_date'])
        
        if self.maintenance_data.get('next_service_mileage'):
            self.next_mileage_var.set(str(self.maintenance_data['next_service_mileage']))
        
        if self.maintenance_data['warranty_period']:
            self.warranty_period_var.set(str(self.maintenance_data['warranty_period']))

        if self.maintenance_data['warranty_expiry']:
            self.warranty_expiry_var.set(self.maintenance_data['warranty_expiry'])

        self.receipt_number_var.set(self.maintenance_data['receipt_number'] or '')

        self.is_scheduled_var.set(self.maintenance_data['is_scheduled'] or False)
        self.is_completed_var.set(self.maintenance_data['is_completed'] if self.maintenance_data['is_completed'] is not None else True)

        if self.maintenance_data['notes']:
            self.notes_text.insert('1.0', self.maintenance_data['notes'])
    
    def validate_fields(self):
        """Valida os campos obrigatórios"""
        if not self.vehicle_var.get():
            messagebox.showerror("Erro de Validação", "Selecione um veículo!")
            return False

        if not self.maintenance_type_var.get():
            messagebox.showerror("Erro de Validação", "Selecione o tipo de manutenção!")
            return False

        description = self.description_text.get('1.0', tk.END).strip()
        if not description:
            messagebox.showerror("Erro de Validação", "Descrição é obrigatória!")
            return False

        service_date = self.service_date_var.get().strip()
        if not service_date:
            messagebox.showerror("Erro de Validação", "Data do serviço é obrigatória!")
            return False

        cost_str = self.cost_var.get().strip()
        if not cost_str:
            messagebox.showerror("Erro de Validação", "Custo é obrigatório!")
            return False

        # Validar formato do custo (aceitar vírgula brasileira)
        try:
            cost_normalized = cost_str.replace(',', '.')
            cost_value = float(cost_normalized)
            if cost_value <= 0:
                messagebox.showerror("Erro de Validação", "Custo deve ser maior que zero!")
                return False
        except ValueError:
            messagebox.showerror("Erro de Validação",
                               f"Custo deve ser um número válido!\n" +
                               f"Valor informado: '{cost_str}'\n" +
                               f"Use formato: 150.50 ou 150,50")
            return False

        # Validar quilometragem se informada
        mileage_str = self.mileage_var.get().strip()
        if mileage_str:
            try:
                mileage_value = int(mileage_str)
                if mileage_value < 0:
                    messagebox.showerror("Erro de Validação", "Quilometragem não pode ser negativa!")
                    return False
            except ValueError:
                messagebox.showerror("Erro de Validação",
                                   f"Quilometragem deve ser um número inteiro!\n" +
                                   f"Valor informado: '{mileage_str}'")
                return False

        return True
    
    def save(self):
        """Salva a manutenção"""
        if not self.validate_fields():
            return

        try:
            # Obter ID do veículo selecionado
            selected_vehicle = self.vehicle_var.get()
            vehicle_id = self.vehicle_options.get(selected_vehicle)

            if not vehicle_id:
                messagebox.showerror("Erro", "Veículo selecionado inválido!")
                return

            # Converter e validar data do formato brasileiro para ISO
            service_date = self.service_date_var.get().strip()
            if service_date:
                try:
                    # Tentar converter de dd/mm/yyyy para yyyy-mm-dd
                    if '/' in service_date:
                        parts = service_date.split('/')
                        if len(parts) == 3:
                            day, month, year = parts

                            # Validar valores
                            day_int = int(day)
                            month_int = int(month)
                            year_int = int(year)

                            if not (1 <= day_int <= 31):
                                raise ValueError(f"Dia inválido: {day}")
                            if not (1 <= month_int <= 12):
                                raise ValueError(f"Mês inválido: {month}")
                            if not (1900 <= year_int <= 2100):
                                raise ValueError(f"Ano inválido: {year}")

                            # Validação mais específica para dias por mês
                            import calendar
                            if day_int > calendar.monthrange(year_int, month_int)[1]:
                                raise ValueError(f"Dia {day} é inválido para o mês {month}/{year}")

                            service_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                        else:
                            raise ValueError("Formato de data inválido")
                except ValueError as e:
                    if "invalid literal" in str(e):
                        raise ValueError(f"Data do serviço inválida: '{service_date}'. Use formato dd/mm/aaaa com números válidos")
                    raise ValueError(f"Data do serviço inválida: {str(e)}")
                except Exception as e:
                    raise ValueError(f"Data do serviço inválida: '{service_date}'. Use formato dd/mm/aaaa")

            # Converter e validar next_service_date se necessário
            next_service_date = self.next_service_date_var.get().strip() or None
            if next_service_date and '/' in next_service_date:
                try:
                    parts = next_service_date.split('/')
                    if len(parts) == 3:
                        day, month, year = parts

                        # Validar valores
                        day_int = int(day)
                        month_int = int(month)
                        year_int = int(year)

                        if not (1 <= day_int <= 31):
                            raise ValueError(f"Dia inválido: {day}")
                        if not (1 <= month_int <= 12):
                            raise ValueError(f"Mês inválido: {month}")
                        if not (1900 <= year_int <= 2100):
                            raise ValueError(f"Ano inválido: {year}")

                        import calendar
                        if day_int > calendar.monthrange(year_int, month_int)[1]:
                            raise ValueError(f"Dia {day} é inválido para o mês {month}/{year}")

                        next_service_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                    else:
                        raise ValueError("Formato de data inválido")
                except ValueError as e:
                    if "invalid literal" in str(e):
                        raise ValueError(f"Data do próximo serviço inválida: '{next_service_date}'. Use formato dd/mm/aaaa com números válidos")
                    raise ValueError(f"Data do próximo serviço inválida: {str(e)}")
                except Exception as e:
                    raise ValueError(f"Data do próximo serviço inválida: '{next_service_date}'. Use formato dd/mm/aaaa")

            # Converter e validar warranty_expiry se necessário
            warranty_expiry = self.warranty_expiry_var.get().strip() or None
            if warranty_expiry and '/' in warranty_expiry:
                try:
                    parts = warranty_expiry.split('/')
                    if len(parts) == 3:
                        day, month, year = parts

                        # Validar valores
                        day_int = int(day)
                        month_int = int(month)
                        year_int = int(year)

                        if not (1 <= day_int <= 31):
                            raise ValueError(f"Dia inválido: {day}")
                        if not (1 <= month_int <= 12):
                            raise ValueError(f"Mês inválido: {month}")
                        if not (1900 <= year_int <= 2100):
                            raise ValueError(f"Ano inválido: {year}")

                        import calendar
                        if day_int > calendar.monthrange(year_int, month_int)[1]:
                            raise ValueError(f"Dia {day} é inválido para o mês {month}/{year}")

                        warranty_expiry = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                    else:
                        raise ValueError("Formato de data inválido")
                except ValueError as e:
                    if "invalid literal" in str(e):
                        raise ValueError(f"Data de expiração da garantia inválida: '{warranty_expiry}'. Use formato dd/mm/aaaa com números válidos")
                    raise ValueError(f"Data de expiração da garantia inválida: {str(e)}")
                except Exception as e:
                    raise ValueError(f"Data de expiração da garantia inválida: '{warranty_expiry}'. Use formato dd/mm/aaaa")

            # Converter e validar valores numéricos
            try:
                # Custo (obrigatório)
                cost_str = self.cost_var.get().strip().replace(',', '.')
                cost = float(cost_str)
                if cost <= 0:
                    raise ValueError("Custo deve ser maior que zero")
            except ValueError as e:
                if "could not convert" in str(e) or "invalid literal" in str(e):
                    raise ValueError(f"Custo inválido: '{self.cost_var.get()}'. Use formato: 150.50 ou 150,50")
                raise

            try:
                # Quilometragem (opcional)
                mileage_at_service = None
                mileage_str = self.mileage_var.get().strip()
                if mileage_str:
                    mileage_at_service = int(mileage_str)
                    if mileage_at_service < 0:
                        raise ValueError("Quilometragem não pode ser negativa")
            except ValueError as e:
                if "invalid literal" in str(e):
                    raise ValueError(f"Quilometragem inválida: '{mileage_str}'. Deve ser um número inteiro")
                raise

            try:
                # Próxima quilometragem (opcional)
                next_service_mileage = None
                next_mileage_str = self.next_mileage_var.get().strip()
                if next_mileage_str:
                    next_service_mileage = int(next_mileage_str)
                    if next_service_mileage < 0:
                        raise ValueError("Próxima quilometragem não pode ser negativa")
            except ValueError as e:
                if "invalid literal" in str(e):
                    raise ValueError(f"Próxima quilometragem inválida: '{next_mileage_str}'. Deve ser um número inteiro")
                raise

            try:
                # Período de garantia (opcional)
                warranty_period = None
                warranty_str = self.warranty_period_var.get().strip()
                if warranty_str:
                    warranty_period = int(warranty_str)
                    if warranty_period < 0:
                        raise ValueError("Período de garantia não pode ser negativo")
            except ValueError as e:
                if "invalid literal" in str(e):
                    raise ValueError(f"Período de garantia inválido: '{warranty_str}'. Deve ser um número inteiro")
                raise

            # Preparar dados
            maintenance_data = {
                'vehicle_id': vehicle_id,
                'maintenance_type': self.maintenance_type_var.get(),
                'description': self.description_text.get('1.0', tk.END).strip(),
                'service_date': service_date,
                'mileage_at_service': mileage_at_service,
                'cost': cost,
                'service_provider': self.service_provider_var.get().strip() or None,
                'next_service_date': next_service_date,
                'next_service_mileage': next_service_mileage,
                'warranty_period': warranty_period,
                'warranty_expiry': warranty_expiry,
                'receipt_number': self.receipt_number_var.get().strip() or None,
                'is_scheduled': self.is_scheduled_var.get(),
                'is_completed': self.is_completed_var.get(),
                'notes': self.notes_text.get('1.0', tk.END).strip() or None
            }
            
            if self.is_edit_mode:
                # Atualizar manutenção existente
                self.db_manager.update_maintenance_record(self.maintenance_data['id'], maintenance_data)
                messagebox.showinfo("Sucesso", "Manutenção atualizada com sucesso!")
            else:
                # Criar nova manutenção
                maintenance_id = self.db_manager.add_maintenance_record(self.user_id, maintenance_data)
                messagebox.showinfo("Sucesso", "Manutenção registrada com sucesso!")

            # Chamar callback se fornecido
            if self.callback:
                self.callback()

            # Fechar janela
            self.window.destroy()

        except ValueError as e:
            messagebox.showerror("Erro", f"Dados inválidos: {str(e)}")
        except Exception as e:
            import traceback
            traceback.print_exc()  # Para debug no console
            messagebox.showerror("Erro", f"Erro ao salvar manutenção: {str(e)}")
    
    def cancel(self):
        """Cancela a operação"""
        self.window.destroy()
