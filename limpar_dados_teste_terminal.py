#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para limpar dados automáticos de teste - Versão Terminal
"""

import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager

def limpar_dados_teste_terminal():
    """Remove dados automáticos de teste do banco de dados - versão terminal"""
    print("🧹 LIMPEZA DE DADOS DE TESTE (TERMINAL)")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        user_id = user_data['id']
        
        # Verificar dados de teste existentes
        print("\n🔍 VERIFICANDO DADOS DE TESTE...")
        
        # Contar transações de teste
        result = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM transactions 
            WHERE user_id = ? AND (
                description LIKE '%teste%' OR 
                description LIKE '%Teste%' OR
                description LIKE '%TESTE%' OR
                description LIKE '%Compra à vista%' OR
                description LIKE '%Compra parcelada%' OR
                description LIKE '%Compra em 12x%' OR
                description LIKE '%Teste 15 parcelas%'
            )
        """, (user_id,))
        test_transactions = result[0]['count'] if result else 0
        
        # Contar categorias de teste
        result = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM categories 
            WHERE user_id = ? AND (
                name LIKE '%teste%' OR 
                name LIKE '%Teste%' OR
                name LIKE '%TESTE%' OR
                description LIKE '%teste%'
            )
        """, (user_id,))
        test_categories = result[0]['count'] if result else 0
        
        # Contar carteiras de teste
        result = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM wallets 
            WHERE user_id = ? AND (
                name LIKE '%teste%' OR 
                name LIKE '%Teste%' OR
                name LIKE '%TESTE%'
            )
        """, (user_id,))
        test_wallets = result[0]['count'] if result else 0
        
        print(f"   📊 Transações de teste: {test_transactions}")
        print(f"   🏷️  Categorias de teste: {test_categories}")
        print(f"   💳 Carteiras de teste: {test_wallets}")
        
        if test_transactions == 0 and test_categories == 0 and test_wallets == 0:
            print("\n✅ NENHUM DADO DE TESTE ENCONTRADO!")
            print("   Não há nada para limpar.")
            input("\nPressione Enter para sair...")
            return
        
        # Mostrar detalhes das transações
        if test_transactions > 0:
            print("\n📋 TRANSAÇÕES QUE SERÃO REMOVIDAS:")
            
            test_trans_details = db_manager.execute_query("""
                SELECT description, amount, transaction_date, installments, installment_number
                FROM transactions 
                WHERE user_id = ? AND (
                    description LIKE '%teste%' OR 
                    description LIKE '%Teste%' OR
                    description LIKE '%TESTE%' OR
                    description LIKE '%Compra à vista%' OR
                    description LIKE '%Compra parcelada%' OR
                    description LIKE '%Compra em 12x%' OR
                    description LIKE '%Teste 15 parcelas%'
                )
                ORDER BY description, installment_number
                LIMIT 20
            """, (user_id,))
            
            for trans in test_trans_details:
                if trans['installments'] > 1:
                    print(f"   • {trans['description']} - R$ {trans['amount']:.2f} "
                          f"({trans['installment_number']}/{trans['installments']})")
                else:
                    print(f"   • {trans['description']} - R$ {trans['amount']:.2f}")
            
            if len(test_trans_details) == 20:
                print("   ... (e mais transações)")
        
        if test_categories > 0:
            print("\n🏷️  CATEGORIAS QUE SERÃO REMOVIDAS:")
            
            test_cat_details = db_manager.execute_query("""
                SELECT name, category_type FROM categories 
                WHERE user_id = ? AND (
                    name LIKE '%teste%' OR 
                    name LIKE '%Teste%' OR
                    name LIKE '%TESTE%' OR
                    description LIKE '%teste%'
                )
            """, (user_id,))
            
            for cat in test_cat_details:
                tipo = "Receita" if cat['category_type'] == 'income' else "Despesa"
                print(f"   • {cat['name']} ({tipo})")
        
        print("\n⚠️  ATENÇÃO: Esta ação não pode ser desfeita!")
        print("✅ Dados reais do usuário serão preservados.")
        
        # Confirmação
        print("\n" + "=" * 50)
        resposta = input(f"Deseja remover {test_transactions} transações e {test_categories} categorias de teste? (s/n): ")
        
        if resposta.lower() in ['s', 'sim', 'y', 'yes']:
            print("\n🧹 EXECUTANDO LIMPEZA...")
            
            # Remover transações de teste
            if test_transactions > 0:
                db_manager.execute_query("""
                    DELETE FROM transactions 
                    WHERE user_id = ? AND (
                        description LIKE '%teste%' OR 
                        description LIKE '%Teste%' OR
                        description LIKE '%TESTE%' OR
                        description LIKE '%Compra à vista%' OR
                        description LIKE '%Compra parcelada%' OR
                        description LIKE '%Compra em 12x%' OR
                        description LIKE '%Teste 15 parcelas%'
                    )
                """, (user_id,))
                print(f"   ✅ {test_transactions} transações de teste removidas")
            
            # Remover categorias de teste (apenas se não estiverem em uso)
            if test_categories > 0:
                # Verificar quais categorias não estão em uso
                unused_test_categories = db_manager.execute_query("""
                    SELECT c.id, c.name FROM categories c
                    WHERE c.user_id = ? AND (
                        c.name LIKE '%teste%' OR 
                        c.name LIKE '%Teste%' OR
                        c.name LIKE '%TESTE%' OR
                        c.description LIKE '%teste%'
                    ) AND c.id NOT IN (
                        SELECT DISTINCT category_id FROM transactions WHERE user_id = ?
                    )
                """, (user_id, user_id))
                
                for cat in unused_test_categories:
                    db_manager.execute_query("DELETE FROM categories WHERE id = ?", (cat['id'],))
                
                print(f"   ✅ {len(unused_test_categories)} categorias de teste removidas")
                
                if len(unused_test_categories) < test_categories:
                    remaining = test_categories - len(unused_test_categories)
                    print(f"   ⚠️  {remaining} categorias de teste mantidas (em uso)")
            
            # Remover carteiras de teste (apenas se não estiverem em uso)
            if test_wallets > 0:
                unused_test_wallets = db_manager.execute_query("""
                    SELECT w.id, w.name FROM wallets w
                    WHERE w.user_id = ? AND (
                        w.name LIKE '%teste%' OR 
                        w.name LIKE '%Teste%' OR
                        w.name LIKE '%TESTE%'
                    ) AND w.id NOT IN (
                        SELECT DISTINCT wallet_id FROM transactions WHERE user_id = ?
                    )
                """, (user_id, user_id))
                
                for wallet in unused_test_wallets:
                    db_manager.execute_query("DELETE FROM wallets WHERE id = ?", (wallet['id'],))
                
                print(f"   ✅ {len(unused_test_wallets)} carteiras de teste removidas")
            
            print("\n✅ LIMPEZA CONCLUÍDA COM SUCESSO!")
            print("Reinicie a aplicação para ver as mudanças.")
            
        else:
            print("❌ Limpeza cancelada")
        
        input("\nPressione Enter para sair...")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()
        input("\nPressione Enter para sair...")

if __name__ == "__main__":
    limpar_dados_teste_terminal()
