#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Gestão Financeira - Versão Consolidada para Executável
Todas as funcionalidades em um único arquivo
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import sqlite3
import hashlib
import os
import sys
from datetime import datetime, date, timedelta
import json
from pathlib import Path
import calendar
import locale

# Configurar encoding
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'pt_BR.UTF-8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'Portuguese_Brazil.1252')
        except:
            pass

class DatabaseManager:
    """Gerenciador de banco de dados consolidado"""
    
    def __init__(self, db_path="gestao_financeira.db"):
        self.db_path = db_path
        self.initialize_database()
    
    def get_connection(self):
        """Retorna conexão com o banco"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def initialize_database(self):
        """Inicializa todas as tabelas"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Tabela de usuários
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    full_name TEXT NOT NULL,
                    is_admin BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela de carteiras
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS wallets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    wallet_type TEXT NOT NULL,
                    balance REAL DEFAULT 0.0,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela de transações
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    wallet_id INTEGER,
                    type TEXT NOT NULL,
                    amount REAL NOT NULL,
                    description TEXT,
                    category TEXT,
                    date DATE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (wallet_id) REFERENCES wallets (id)
                )
            ''')
            
            # Tabela de veículos
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS vehicles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    brand TEXT,
                    model TEXT,
                    year INTEGER,
                    license_plate TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela de manutenção
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS vehicle_maintenance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    vehicle_id INTEGER,
                    type TEXT NOT NULL,
                    description TEXT,
                    cost REAL,
                    date DATE NOT NULL,
                    mileage INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (vehicle_id) REFERENCES vehicles (id)
                )
            ''')
            
            # Tabela de combustível
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS fuel_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    vehicle_id INTEGER,
                    fuel_type TEXT,
                    liters REAL,
                    cost REAL,
                    mileage INTEGER,
                    date DATE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (vehicle_id) REFERENCES vehicles (id)
                )
            ''')
            
            conn.commit()
            
            # Criar usuário admin padrão
            self.create_default_admin()
            
        except Exception as e:
            print(f"Erro ao inicializar banco: {e}")
        finally:
            conn.close()
    
    def create_default_admin(self):
        """Cria usuário admin padrão"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Verificar se admin já existe
            cursor.execute("SELECT id FROM users WHERE username = ?", ('admin',))
            if cursor.fetchone():
                conn.close()
                return
            
            # Criar hash da senha
            password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
            
            cursor.execute('''
                INSERT INTO users (username, password_hash, email, full_name, is_admin)
                VALUES (?, ?, ?, ?, ?)
            ''', ('admin', password_hash, '<EMAIL>', 'Administrador do Sistema', True))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Erro ao criar admin: {e}")
    
    def execute_query(self, query, params=None):
        """Executa query e retorna resultados"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if query.strip().upper().startswith('SELECT'):
                rows = cursor.fetchall()
                if rows:
                    columns = [description[0] for description in cursor.description]
                    return [dict(zip(columns, row)) for row in rows]
                return []
            else:
                conn.commit()
                return cursor.rowcount
        finally:
            conn.close()

class AuthManager:
    """Gerenciador de autenticação consolidado"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
    
    def hash_password(self, password):
        """Gera hash da senha"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password, hashed_password):
        """Verifica senha"""
        return hashlib.sha256(password.encode()).hexdigest() == hashed_password
    
    def authenticate_user(self, username, password):
        """Autentica usuário"""
        query = '''
            SELECT id, username, password_hash, email, full_name, is_admin
            FROM users WHERE username = ?
        '''
        result = self.db_manager.execute_query(query, (username,))
        
        if not result:
            return None
        
        user_data = result[0]
        
        if self.verify_password(password, user_data['password_hash']):
            return user_data
        
        return None
    
    def create_user(self, username, password, email, full_name, is_admin=False):
        """Cria novo usuário"""
        password_hash = self.hash_password(password)
        
        query = '''
            INSERT INTO users (username, password_hash, email, full_name, is_admin)
            VALUES (?, ?, ?, ?, ?)
        '''
        
        try:
            self.db_manager.execute_query(query, (username, password_hash, email, full_name, is_admin))
            return True
        except:
            return False

class LoginWindow:
    """Janela de login consolidada"""
    
    def __init__(self, parent, auth_manager, success_callback):
        self.parent = parent
        self.auth_manager = auth_manager
        self.success_callback = success_callback
        
        self.window = tk.Toplevel(parent)
        self.window.title("Login - Sistema de Gestão Financeira")
        self.window.geometry("450x350")
        self.window.resizable(False, False)
        
        # Centralizar
        self.center_window()
        
        # Criar interface
        self.create_widgets()
        
        # Configurar foco
        self.window.grab_set()
        self.window.focus_force()
    
    def center_window(self):
        """Centraliza a janela"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - 225
        y = (self.window.winfo_screenheight() // 2) - 175
        self.window.geometry(f"450x350+{x}+{y}")
    
    def create_widgets(self):
        """Cria os widgets da interface"""
        # Frame principal
        main_frame = tk.Frame(self.window, bg='#f0f0f0', padx=40, pady=40)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title_label = tk.Label(main_frame, text="Sistema de Gestão Financeira", 
                              font=('Arial', 18, 'bold'), bg='#f0f0f0', fg='#2c3e50')
        title_label.pack(pady=(0, 30))
        
        # Frame do formulário
        form_frame = tk.Frame(main_frame, bg='#f0f0f0')
        form_frame.pack(fill='x')
        
        # Usuário
        tk.Label(form_frame, text="Usuário:", font=('Arial', 11), 
                bg='#f0f0f0', fg='#34495e').pack(anchor='w', pady=(0, 5))
        
        self.username_entry = tk.Entry(form_frame, font=('Arial', 12), width=25, relief='solid', bd=1)
        self.username_entry.pack(fill='x', pady=(0, 15))
        self.username_entry.insert(0, 'admin')
        
        # Senha
        tk.Label(form_frame, text="Senha:", font=('Arial', 11), 
                bg='#f0f0f0', fg='#34495e').pack(anchor='w', pady=(0, 5))
        
        self.password_entry = tk.Entry(form_frame, font=('Arial', 12), width=25, 
                                      show='*', relief='solid', bd=1)
        self.password_entry.pack(fill='x', pady=(0, 25))
        self.password_entry.insert(0, 'admin123')
        
        # Botão login
        login_btn = tk.Button(form_frame, text="ENTRAR", command=self.login,
                             font=('Arial', 12, 'bold'), bg='#3498db', fg='white',
                             padx=30, pady=10, relief='flat', cursor='hand2')
        login_btn.pack(pady=10)
        
        # Informações
        info_label = tk.Label(main_frame, text="Usuário padrão: admin / admin123", 
                             font=('Arial', 9), bg='#f0f0f0', fg='#7f8c8d')
        info_label.pack(pady=(20, 0))
        
        # Bind Enter
        self.window.bind('<Return>', lambda e: self.login())
        self.username_entry.focus()
    
    def login(self):
        """Executa o login"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("Erro", "Preencha todos os campos!")
            return
        
        user = self.auth_manager.authenticate_user(username, password)
        
        if user:
            self.window.destroy()
            self.success_callback(user)
        else:
            messagebox.showerror("Erro", "Usuário ou senha inválidos!")
            self.password_entry.delete(0, tk.END)
            self.password_entry.focus()

class MainWindow:
    """Janela principal consolidada"""
    
    def __init__(self, parent, db_manager, auth_manager, user_data, logout_callback):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.user_data = user_data
        self.logout_callback = logout_callback
        
        self.setup_window()
        self.create_widgets()
        self.load_data()
    
    def setup_window(self):
        """Configura a janela principal"""
        self.parent.title(f"Sistema de Gestão Financeira - {self.user_data['full_name']}")
        self.parent.geometry("1200x800")
        
        # Centralizar
        self.parent.update_idletasks()
        x = (self.parent.winfo_screenwidth() // 2) - 600
        y = (self.parent.winfo_screenheight() // 2) - 400
        self.parent.geometry(f"1200x800+{x}+{y}")
    
    def create_widgets(self):
        """Cria os widgets da interface principal"""
        # Frame superior
        header_frame = tk.Frame(self.parent, bg='#2c3e50', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Título no header
        title_label = tk.Label(header_frame, text="Sistema de Gestão Financeira", 
                              font=('Arial', 20, 'bold'), bg='#2c3e50', fg='white')
        title_label.pack(side='left', padx=20, pady=20)
        
        # Info do usuário
        user_label = tk.Label(header_frame, text=f"Usuário: {self.user_data['full_name']}", 
                             font=('Arial', 11), bg='#2c3e50', fg='#ecf0f1')
        user_label.pack(side='right', padx=20, pady=20)
        
        # Notebook para abas
        self.notebook = ttk.Notebook(self.parent)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Criar abas
        self.create_dashboard_tab()
        self.create_wallets_tab()
        self.create_transactions_tab()
        self.create_vehicles_tab()
        
        # Frame inferior
        footer_frame = tk.Frame(self.parent, bg='#ecf0f1', height=50)
        footer_frame.pack(fill='x')
        footer_frame.pack_propagate(False)
        
        # Botões do footer
        tk.Button(footer_frame, text="Sair", command=self.logout_callback,
                 font=('Arial', 10), padx=20, pady=5).pack(side='right', padx=10, pady=10)
        
        # Status
        tk.Label(footer_frame, text="Sistema funcionando - Versão Consolidada", 
                font=('Arial', 9), bg='#ecf0f1', fg='#27ae60').pack(side='left', padx=10, pady=15)
    
    def create_dashboard_tab(self):
        """Cria aba do dashboard"""
        dashboard_frame = tk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="📊 Dashboard")
        
        # Conteúdo do dashboard
        content_frame = tk.Frame(dashboard_frame, padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(content_frame, text="Dashboard Financeiro", 
                font=('Arial', 18, 'bold')).pack(pady=(0, 20))
        
        # Cards de resumo
        cards_frame = tk.Frame(content_frame)
        cards_frame.pack(fill='x', pady=20)
        
        # Card Carteiras
        wallet_card = tk.LabelFrame(cards_frame, text="💰 Carteiras", 
                                   font=('Arial', 12, 'bold'), padx=10, pady=10)
        wallet_card.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        wallet_count = len(self.db_manager.execute_query("SELECT id FROM wallets"))
        wallet_total = sum([w.get('balance', 0) for w in self.db_manager.execute_query("SELECT balance FROM wallets")])
        
        tk.Label(wallet_card, text=f"Total: R$ {wallet_total:.2f}", 
                font=('Arial', 16, 'bold'), fg='#27ae60').pack(pady=5)
        tk.Label(wallet_card, text=f"{wallet_count} carteiras", 
                font=('Arial', 11)).pack()
        
        # Card Transações
        trans_card = tk.LabelFrame(cards_frame, text="💳 Transações", 
                                  font=('Arial', 12, 'bold'), padx=10, pady=10)
        trans_card.pack(side='left', fill='both', expand=True, padx=10)
        
        trans_count = len(self.db_manager.execute_query("SELECT id FROM transactions"))
        
        tk.Label(trans_card, text=f"Total: {trans_count}", 
                font=('Arial', 16, 'bold'), fg='#3498db').pack(pady=5)
        tk.Label(trans_card, text="transações", 
                font=('Arial', 11)).pack()
        
        # Card Veículos
        vehicle_card = tk.LabelFrame(cards_frame, text="🚗 Veículos", 
                                    font=('Arial', 12, 'bold'), padx=10, pady=10)
        vehicle_card.pack(side='left', fill='both', expand=True, padx=(10, 0))
        
        vehicle_count = len(self.db_manager.execute_query("SELECT id FROM vehicles"))
        
        tk.Label(vehicle_card, text=f"Total: {vehicle_count}", 
                font=('Arial', 16, 'bold'), fg='#e74c3c').pack(pady=5)
        tk.Label(vehicle_card, text="veículos", 
                font=('Arial', 11)).pack()
        
        # Informações do sistema
        info_frame = tk.LabelFrame(content_frame, text="ℹ️ Informações do Sistema", 
                                  font=('Arial', 12, 'bold'), padx=20, pady=20)
        info_frame.pack(fill='both', expand=True, pady=20)
        
        info_text = f"""
Sistema de Gestão Financeira - Versão Consolidada

✅ Funcionalidades Disponíveis:
• Controle de carteiras e saldos
• Registro de transações (receitas/despesas)
• Gestão de veículos
• Controle de manutenção e combustível
• Dashboard com resumos financeiros
• Sistema de usuários com autenticação

📊 Estatísticas Atuais:
• Carteiras: {wallet_count}
• Transações: {trans_count}
• Veículos: {vehicle_count}
• Usuário: {self.user_data['full_name']}

🎯 Esta versão consolidada foi criada especificamente para funcionar
como executável, com todas as funcionalidades em um único arquivo.
        """
        
        tk.Label(info_frame, text=info_text, font=('Arial', 10), 
                justify='left', anchor='nw').pack(fill='both', expand=True)
    
    def create_wallets_tab(self):
        """Cria aba de carteiras"""
        wallets_frame = tk.Frame(self.notebook)
        self.notebook.add(wallets_frame, text="💰 Carteiras")
        
        content_frame = tk.Frame(wallets_frame, padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(content_frame, text="Gerenciamento de Carteiras", 
                font=('Arial', 18, 'bold')).pack(pady=(0, 20))
        
        # Botões
        btn_frame = tk.Frame(content_frame)
        btn_frame.pack(fill='x', pady=(0, 20))
        
        tk.Button(btn_frame, text="➕ Nova Carteira", command=self.add_wallet,
                 font=('Arial', 10), padx=15, pady=5).pack(side='left', padx=(0, 10))
        
        tk.Button(btn_frame, text="🔄 Atualizar", command=self.load_wallets,
                 font=('Arial', 10), padx=15, pady=5).pack(side='left')
        
        # Lista de carteiras
        self.wallets_tree = ttk.Treeview(content_frame, columns=('name', 'type', 'balance'), show='headings')
        self.wallets_tree.heading('name', text='Nome')
        self.wallets_tree.heading('type', text='Tipo')
        self.wallets_tree.heading('balance', text='Saldo')
        
        self.wallets_tree.column('name', width=300)
        self.wallets_tree.column('type', width=200)
        self.wallets_tree.column('balance', width=150)
        
        self.wallets_tree.pack(fill=tk.BOTH, expand=True)
        
        # Carregar carteiras
        self.load_wallets()
    
    def create_transactions_tab(self):
        """Cria aba de transações"""
        trans_frame = tk.Frame(self.notebook)
        self.notebook.add(trans_frame, text="💳 Transações")
        
        content_frame = tk.Frame(trans_frame, padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(content_frame, text="Registro de Transações", 
                font=('Arial', 18, 'bold')).pack(pady=(0, 20))
        
        tk.Label(content_frame, text="Funcionalidade em desenvolvimento...", 
                font=('Arial', 12)).pack(pady=50)
    
    def create_vehicles_tab(self):
        """Cria aba de veículos"""
        vehicles_frame = tk.Frame(self.notebook)
        self.notebook.add(vehicles_frame, text="🚗 Veículos")
        
        content_frame = tk.Frame(vehicles_frame, padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(content_frame, text="Gestão de Veículos", 
                font=('Arial', 18, 'bold')).pack(pady=(0, 20))
        
        tk.Label(content_frame, text="Funcionalidade em desenvolvimento...", 
                font=('Arial', 12)).pack(pady=50)
    
    def load_data(self):
        """Carrega dados iniciais"""
        self.load_wallets()
    
    def load_wallets(self):
        """Carrega lista de carteiras"""
        # Limpar árvore
        for item in self.wallets_tree.get_children():
            self.wallets_tree.delete(item)
        
        # Carregar carteiras
        wallets = self.db_manager.execute_query("SELECT * FROM wallets ORDER BY name")
        
        for wallet in wallets:
            self.wallets_tree.insert('', 'end', values=(
                wallet['name'],
                wallet['wallet_type'],
                f"R$ {wallet['balance']:.2f}"
            ))
    
    def add_wallet(self):
        """Adiciona nova carteira"""
        name = simpledialog.askstring("Nova Carteira", "Nome da carteira:")
        if not name:
            return
        
        wallet_type = simpledialog.askstring("Nova Carteira", "Tipo da carteira:")
        if not wallet_type:
            wallet_type = "Conta Corrente"
        
        try:
            self.db_manager.execute_query(
                "INSERT INTO wallets (name, wallet_type, balance) VALUES (?, ?, ?)",
                (name, wallet_type, 0.0)
            )
            messagebox.showinfo("Sucesso", "Carteira criada com sucesso!")
            self.load_wallets()
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao criar carteira: {e}")

def main():
    """Função principal consolidada"""
    print("Iniciando Sistema de Gestão Financeira - Versão Consolidada...")
    
    try:
        # Inicializar banco de dados
        print("Inicializando banco de dados...")
        db_manager = DatabaseManager()
        
        # Inicializar autenticação
        print("Inicializando sistema de autenticação...")
        auth_manager = AuthManager(db_manager)
        
        # Criar janela principal
        root = tk.Tk()
        root.withdraw()  # Esconder inicialmente
        
        def on_login_success(user_data):
            """Callback de login bem-sucedido"""
            root.deiconify()
            MainWindow(root, db_manager, auth_manager, user_data, root.quit)
        
        # Mostrar janela de login
        print("Criando interface de login...")
        LoginWindow(root, auth_manager, on_login_success)
        
        # Iniciar loop principal
        print("Sistema iniciado com sucesso!")
        root.mainloop()
        
        print("Sistema encerrado.")
        
    except Exception as e:
        print(f"Erro no sistema: {e}")
        import traceback
        traceback.print_exc()
        
        try:
            messagebox.showerror("Erro Fatal", f"Erro no sistema:\n{str(e)}")
        except:
            pass

if __name__ == "__main__":
    main()
