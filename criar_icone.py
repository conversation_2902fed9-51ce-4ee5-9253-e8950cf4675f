#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Criar ícone personalizado para o aplicativo
"""

from PIL import Image, ImageDraw, ImageFont
import os

def criar_icone_app():
    """Cria ícone personalizado para o aplicativo"""
    print("🎨 CRIANDO ÍCONE PERSONALIZADO")
    print("=" * 40)
    
    try:
        # Tamanhos de ícone
        sizes = [16, 32, 48, 64, 128, 256]
        
        # Criar pasta de ícones se não existir
        if not os.path.exists('assets'):
            os.makedirs('assets')
        
        # Cores
        bg_color = "#1a202c"  # Fundo escuro moderno
        primary_color = "#4299e1"  # Azul principal
        accent_color = "#68d391"  # Verde accent
        text_color = "#ffffff"  # Branco
        
        for size in sizes:
            print(f"   📐 Criando ícone {size}x{size}...")
            
            # Criar imagem
            img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # Fundo circular
            margin = size // 8
            draw.ellipse([margin, margin, size-margin, size-margin], 
                        fill=bg_color, outline=primary_color, width=max(1, size//32))
            
            # Símbolo do dinheiro ($)
            font_size = size // 2
            try:
                # Tentar usar fonte do sistema
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                try:
                    font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", font_size)
                except:
                    font = ImageFont.load_default()
            
            # Desenhar símbolo
            symbol = "💰"
            if size >= 32:
                # Para ícones maiores, usar emoji
                try:
                    # Calcular posição centralizada
                    bbox = draw.textbbox((0, 0), symbol, font=font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]
                    x = (size - text_width) // 2
                    y = (size - text_height) // 2 - size//16
                    
                    draw.text((x, y), symbol, fill=text_color, font=font)
                except:
                    # Fallback para símbolo $
                    symbol = "$"
                    bbox = draw.textbbox((0, 0), symbol, font=font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]
                    x = (size - text_width) // 2
                    y = (size - text_height) // 2
                    
                    draw.text((x, y), symbol, fill=primary_color, font=font)
            else:
                # Para ícones pequenos, usar formas simples
                center = size // 2
                radius = size // 4
                draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                           fill=primary_color)
            
            # Salvar PNG
            png_path = f'assets/app_icon_{size}.png'
            img.save(png_path, 'PNG')
            print(f"   ✅ Salvo: {png_path}")
        
        # Criar ícone ICO (Windows)
        print(f"   🪟 Criando ícone .ico para Windows...")
        
        # Carregar todas as imagens
        images = []
        for size in sizes:
            img_path = f'assets/app_icon_{size}.png'
            if os.path.exists(img_path):
                images.append(Image.open(img_path))
        
        if images:
            # Salvar como ICO
            ico_path = 'assets/app_icon.ico'
            images[0].save(ico_path, format='ICO', sizes=[(img.width, img.height) for img in images])
            print(f"   ✅ Ícone ICO criado: {ico_path}")
        
        print(f"\n✅ ÍCONES CRIADOS COM SUCESSO!")
        print(f"📁 Pasta: assets/")
        print(f"🖼️  Formatos: PNG (vários tamanhos) + ICO")
        
        return True
        
    except ImportError:
        print("❌ Pillow não está instalado!")
        print("💡 Instale com: pip install Pillow")
        return False
    except Exception as e:
        print(f"❌ Erro ao criar ícone: {str(e)}")
        return False

def criar_icone_simples():
    """Cria ícone simples sem Pillow"""
    print("🎨 CRIANDO ÍCONE SIMPLES (SEM PILLOW)")
    print("=" * 40)
    
    # Criar pasta de assets
    if not os.path.exists('assets'):
        os.makedirs('assets')
    
    # Criar arquivo ICO básico (formato XPM convertido)
    ico_data = """
    Ícone básico criado - use um editor de imagem para melhorar
    """
    
    print("💡 Para criar um ícone personalizado:")
    print("   1. Instale Pillow: pip install Pillow")
    print("   2. Execute novamente este script")
    print("   3. Ou use um editor online: favicon.io")
    
    return False

if __name__ == "__main__":
    # Tentar criar ícone com Pillow
    if not criar_icone_app():
        criar_icone_simples()
