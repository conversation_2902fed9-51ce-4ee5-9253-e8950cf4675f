#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formulário de Transação Completamente Novo e Melhorado
Criado do zero com design moderno e funcionalidades avançadas
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date, timedelta
import calendar

try:
    from tkcalendar import DateEntry
    CALENDAR_AVAILABLE = True
except ImportError:
    CALENDAR_AVAILABLE = False

class TransactionFormNew:
    def __init__(self, parent, db_manager, user_id, transaction_type, transaction_id=None):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.transaction_type = transaction_type  # 'income' ou 'expense'
        self.transaction_id = transaction_id
        self.result = False
        
        # Configurações visuais por tipo
        self.config = self.get_visual_config()
        
        # Criar janela principal
        self.create_main_window()
        
        # Criar interface
        self.create_interface()
        
        # Carregar dados
        self.load_data()
        
        # Configurar eventos
        self.setup_events()
        
        # Focar no primeiro campo
        self.description_entry.focus()
    
    def get_visual_config(self):
        """Retorna configuração visual baseada no tipo de transação"""
        if self.transaction_type == 'income':
            return {
                'title': '📈 Nova Receita' if not self.transaction_id else '📈 Editar Receita',
                'color': '#27ae60',
                'icon': '📈',
                'status_text': '✅ Receita já foi recebida',
                'button_text': '💰 Criar Receita' if not self.transaction_id else '💰 Salvar Receita'
            }
        else:
            return {
                'title': '📉 Nova Despesa' if not self.transaction_id else '📉 Editar Despesa',
                'color': '#e74c3c',
                'icon': '📉',
                'status_text': '✅ Despesa já foi paga',
                'button_text': '💸 Criar Despesa' if not self.transaction_id else '💸 Salvar Despesa'
            }
    
    def create_main_window(self):
        """Cria a janela principal"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.config['title'])
        self.window.geometry("800x950")
        self.window.resizable(True, True)
        self.window.configure(bg='#f8f9fa')

        # Centralizar janela
        self.center_window()

        # Configurar janela
        self.window.transient(self.parent)
        self.window.grab_set()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_interface(self):
        """Cria a interface completa"""
        # Container principal
        main_container = tk.Frame(self.window, bg='#f8f9fa')
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Cabeçalho
        self.create_header(main_container)
        
        # Área de conteúdo com scroll
        self.create_content_area(main_container)
        
        # Rodapé com botões
        self.create_footer(main_container)
    
    def create_header(self, parent):
        """Cria o cabeçalho"""
        header_frame = tk.Frame(parent, bg=self.config['color'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # Título principal
        title_label = tk.Label(
            header_frame, 
            text=self.config['title'],
            font=("Arial", 20, "bold"),
            fg='white',
            bg=self.config['color']
        )
        title_label.pack(expand=True)
        
        # Subtítulo
        subtitle = "Preencha os dados da receita" if self.transaction_type == 'income' else "Preencha os dados da despesa"
        subtitle_label = tk.Label(
            header_frame,
            text=subtitle,
            font=("Arial", 12),
            fg='white',
            bg=self.config['color']
        )
        subtitle_label.pack()
    
    def create_content_area(self, parent):
        """Cria a área de conteúdo com scroll"""
        # Frame principal para conteúdo
        content_frame = tk.Frame(parent, bg='white', relief=tk.RAISED, bd=1)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Canvas para scroll
        canvas = tk.Canvas(content_frame, bg='white', highlightthickness=0)
        scrollbar = ttk.Scrollbar(content_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = tk.Frame(canvas, bg='white')

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=15)
        scrollbar.pack(side="right", fill="y", pady=15)

        # Bind mouse wheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        canvas.bind("<MouseWheel>", _on_mousewheel)

        # Criar seções do formulário
        self.create_basic_info_section()
        self.create_financial_section()
        self.create_dates_section()
        self.create_additional_section()

    def create_basic_info_section(self):
        """Cria seção de informações básicas"""
        section_frame = self.create_section_frame("📋 Informações Básicas", "#3498db")
        
        # Descrição
        self.create_field_row(
            section_frame,
            "📝 Descrição da Transação *",
            "description",
            field_type="entry",
            required=True,
            placeholder="Ex: Salário, Aluguel, Compra no supermercado..."
        )
        
        # Valor
        self.create_field_row(
            section_frame,
            "💰 Valor (R$) *",
            "amount",
            field_type="entry",
            required=True,
            placeholder="0,00",
            width=20
        )
    
    def create_financial_section(self):
        """Cria seção financeira"""
        section_frame = self.create_section_frame("🏦 Informações Financeiras", "#9b59b6")
        
        # Linha com Carteira e Categoria
        row_frame = tk.Frame(section_frame, bg='white')
        row_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Carteira (50%)
        wallet_frame = tk.Frame(row_frame, bg='white')
        wallet_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        tk.Label(
            wallet_frame,
            text="💼 Carteira *",
            font=("Arial", 11, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 5))
        
        self.wallet_combo = ttk.Combobox(
            wallet_frame,
            state="readonly",
            font=("Arial", 11),
            height=8
        )
        self.wallet_combo.pack(fill=tk.X)
        
        # Categoria (50%)
        category_frame = tk.Frame(row_frame, bg='white')
        category_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        category_icon = "📈" if self.transaction_type == 'income' else "📉"
        tk.Label(
            category_frame,
            text=f"{category_icon} Categoria *",
            font=("Arial", 11, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 5))
        
        self.category_combo = ttk.Combobox(
            category_frame,
            state="readonly",
            font=("Arial", 11),
            height=8
        )
        self.category_combo.pack(fill=tk.X)
    
    def create_dates_section(self):
        """Cria seção de datas"""
        section_frame = self.create_section_frame("📅 Datas e Parcelas", "#e67e22")
        
        # Linha com datas
        dates_row = tk.Frame(section_frame, bg='white')
        dates_row.pack(fill=tk.X, pady=(0, 15))
        
        # Data da transação (50%)
        date_frame = tk.Frame(dates_row, bg='white')
        date_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        tk.Label(
            date_frame,
            text="📅 Data da Transação *",
            font=("Arial", 11, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 5))
        
        self.date_entry = self.create_date_field(date_frame, date.today())
        self.date_entry.pack(fill=tk.X)
        
        # Data de vencimento (50%)
        due_date_frame = tk.Frame(dates_row, bg='white')
        due_date_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        tk.Label(
            due_date_frame,
            text="⏰ Data de Vencimento",
            font=("Arial", 11, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 5))
        
        self.due_date_entry = self.create_date_field(due_date_frame, None)
        self.due_date_entry.pack(fill=tk.X)
        
        # Parcelas
        installments_row = tk.Frame(section_frame, bg='white')
        installments_row.pack(fill=tk.X, pady=(0, 10))
        
        installments_frame = tk.Frame(installments_row, bg='white')
        installments_frame.pack(side=tk.LEFT, fill=tk.Y)
        
        tk.Label(
            installments_frame,
            text="🔢 Número de Parcelas",
            font=("Arial", 11, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 5))
        
        self.installments_entry = tk.Entry(
            installments_frame,
            font=("Arial", 11),
            width=15,
            relief=tk.SOLID,
            bd=1
        )
        self.installments_entry.pack()
        self.installments_entry.insert(0, "1")
        
        # Dica sobre parcelas
        tk.Label(
            installments_frame,
            text="💡 1 = À vista | 2+ = Parcelado (máx: 60)",
            font=("Arial", 9),
            bg='white',
            fg='#7f8c8d'
        ).pack(anchor=tk.W, pady=(5, 0))
    
    def create_additional_section(self):
        """Cria seção adicional"""
        section_frame = self.create_section_frame("📝 Informações Adicionais", "#8e44ad")
        
        # Status de pagamento
        self.paid_var = tk.BooleanVar(value=True)
        self.paid_check = tk.Checkbutton(
            section_frame,
            text=self.config['status_text'],
            variable=self.paid_var,
            font=("Arial", 11, "bold"),
            bg='white',
            fg=self.config['color'],
            selectcolor='white',
            activebackground='white',
            activeforeground=self.config['color']
        )
        self.paid_check.pack(anchor=tk.W, pady=(0, 15))
        
        # Observações
        tk.Label(
            section_frame,
            text="📝 Observações",
            font=("Arial", 11, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 5))
        
        self.notes_text = tk.Text(
            section_frame,
            height=4,
            font=("Arial", 10),
            relief=tk.SOLID,
            bd=1,
            wrap=tk.WORD
        )
        self.notes_text.pack(fill=tk.X, pady=(0, 10))
        
        # Placeholder para observações
        self.notes_text.insert('1.0', "Digite observações adicionais sobre esta transação...")
        self.notes_text.bind('<FocusIn>', self.clear_notes_placeholder)
        self.notes_text.bind('<FocusOut>', self.restore_notes_placeholder)
    
    def create_section_frame(self, title, color):
        """Cria um frame de seção"""
        section_frame = tk.LabelFrame(
            self.scrollable_frame,
            text=title,
            font=("Arial", 12, "bold"),
            bg='white',
            fg=color,
            relief=tk.GROOVE,
            bd=2,
            labelanchor='n'
        )
        section_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        section_frame.configure(padx=15, pady=15)
        
        return section_frame
    
    def create_field_row(self, parent, label_text, field_name, field_type="entry", 
                        required=False, placeholder="", width=None):
        """Cria uma linha de campo"""
        # Label
        label = tk.Label(
            parent,
            text=label_text,
            font=("Arial", 11, "bold"),
            bg='white',
            fg='#2c3e50'
        )
        label.pack(anchor=tk.W, pady=(0, 5))
        
        # Campo
        if field_type == "entry":
            field = tk.Entry(
                parent,
                font=("Arial", 11),
                relief=tk.SOLID,
                bd=1,
                width=width
            )
            field.pack(fill=tk.X, pady=(0, 15))
            
            if placeholder:
                field.insert(0, placeholder)
                field.bind('<FocusIn>', lambda e: self.clear_placeholder(e, placeholder))
                field.bind('<FocusOut>', lambda e: self.restore_placeholder(e, placeholder))
            
            setattr(self, f"{field_name}_entry", field)
        
        return field
    
    def create_date_field(self, parent, initial_date):
        """Cria campo de data"""
        if CALENDAR_AVAILABLE:
            date_field = DateEntry(
                parent,
                width=12,
                background=self.config['color'],
                foreground='white',
                borderwidth=2,
                font=("Arial", 11),
                date_pattern='dd/mm/yyyy',
                locale='pt_BR'
            )
            if initial_date:
                date_field.set_date(initial_date)
        else:
            date_field = tk.Entry(
                parent,
                font=("Arial", 11),
                relief=tk.SOLID,
                bd=1
            )
            if initial_date:
                date_field.insert(0, initial_date.strftime("%d/%m/%Y"))
        
        return date_field
    
    def create_footer(self, parent):
        """Cria o rodapé com botões"""
        footer_frame = tk.Frame(parent, bg='#ecf0f1', height=100)
        footer_frame.pack(fill=tk.X, side=tk.BOTTOM)
        footer_frame.pack_propagate(False)

        # Container dos botões
        button_container = tk.Frame(footer_frame, bg='#ecf0f1')
        button_container.pack(expand=True, pady=10)
        
        # Botão cancelar
        cancel_btn = tk.Button(
            button_container,
            text="❌ Cancelar",
            command=self.cancel,
            font=("Arial", 12, "bold"),
            bg='#e74c3c',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        # Botão salvar
        save_btn = tk.Button(
            button_container,
            text=self.config['button_text'],
            command=self.save,
            font=("Arial", 12, "bold"),
            bg=self.config['color'],
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        save_btn.pack(side=tk.LEFT)
        
        # Informações de ajuda
        help_label = tk.Label(
            footer_frame,
            text="💡 Dicas: Tab para navegar • Enter para salvar • Esc para cancelar • * = Obrigatório",
            font=("Arial", 10),
            bg='#ecf0f1',
            fg='#7f8c8d'
        )
        help_label.pack(side=tk.BOTTOM, pady=(0, 10))
    
    def load_data(self):
        """Carrega dados necessários"""
        self.load_wallets()
        self.load_categories()
        
        if self.transaction_id:
            self.load_transaction_data()
    
    def load_wallets(self):
        """Carrega carteiras do usuário"""
        try:
            query = "SELECT id, name FROM wallets WHERE user_id = ? ORDER BY name"
            wallets = self.db_manager.execute_query(query, (self.user_id,))
            
            self.wallets_data = wallets
            wallet_names = [f"💼 {wallet['name']}" for wallet in wallets]
            self.wallet_combo['values'] = wallet_names
            
            if wallets:
                self.wallet_combo.current(0)
                
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar carteiras: {str(e)}")
    
    def load_categories(self):
        """Carrega categorias do tipo apropriado"""
        try:
            query = """
                SELECT id, name FROM categories
                WHERE (user_id = ? OR user_id IS NULL) AND category_type = ? AND is_active = TRUE
                ORDER BY name
            """
            categories = self.db_manager.execute_query(query, (self.user_id, self.transaction_type))
            
            self.categories_data = categories
            category_icon = "📈" if self.transaction_type == 'income' else "📉"
            category_names = [f"{category_icon} {cat['name']}" for cat in categories]
            self.category_combo['values'] = category_names
            
            if categories:
                self.category_combo.current(0)
                
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar categorias: {str(e)}")
    
    def load_transaction_data(self):
        """Carrega dados da transação para edição"""
        if not self.transaction_id:
            return

        try:
            from src.modules.transaction_manager import TransactionManager
            transaction_manager = TransactionManager(self.db_manager)

            # Buscar dados da transação
            transaction = transaction_manager.get_transaction_by_id(self.transaction_id, self.user_id)

            if not transaction:
                messagebox.showerror("Erro", "Transação não encontrada")
                return

            # Preencher campos com os dados da transação
            self.description_entry.delete(0, tk.END)
            self.description_entry.insert(0, transaction['description'])

            self.amount_entry.delete(0, tk.END)
            self.amount_entry.insert(0, str(transaction['amount']))

            # Selecionar carteira
            for i, wallet in enumerate(self.wallets_data):
                if wallet['id'] == transaction['wallet_id']:
                    self.wallet_combo.current(i)
                    break

            # Selecionar categoria
            for i, category in enumerate(self.categories_data):
                if category['id'] == transaction['category_id']:
                    self.category_combo.current(i)
                    break

            # Data da transação
            if transaction['transaction_date']:
                from datetime import datetime
                date_obj = datetime.strptime(transaction['transaction_date'], '%Y-%m-%d')
                self.date_entry.set_date(date_obj.date())

            # Data de vencimento (se existir)
            if hasattr(self, 'due_date_entry') and transaction['due_date']:
                due_date_obj = datetime.strptime(transaction['due_date'], '%Y-%m-%d')
                self.due_date_entry.set_date(due_date_obj.date())

            # Status de pagamento
            if hasattr(self, 'paid_var'):
                self.paid_var.set(transaction['is_paid'])

            # Observações
            if hasattr(self, 'notes_text') and transaction['notes']:
                self.notes_text.delete('1.0', tk.END)
                self.notes_text.insert('1.0', transaction['notes'])

            # Parcelas
            if hasattr(self, 'installments_entry') and transaction['installments']:
                self.installments_entry.delete(0, tk.END)
                self.installments_entry.insert(0, str(transaction['installments']))

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar dados da transação: {str(e)}")
    
    def setup_events(self):
        """Configura eventos"""
        self.window.bind('<Return>', lambda e: self.save())
        self.window.bind('<Escape>', lambda e: self.cancel())
    
    def clear_placeholder(self, event, placeholder):
        """Remove placeholder ao focar"""
        if event.widget.get() == placeholder:
            event.widget.delete(0, tk.END)
    
    def restore_placeholder(self, event, placeholder):
        """Restaura placeholder se vazio"""
        if not event.widget.get():
            event.widget.insert(0, placeholder)
    
    def clear_notes_placeholder(self, event):
        """Remove placeholder das observações"""
        if self.notes_text.get('1.0', tk.END).strip() == "Digite observações adicionais sobre esta transação...":
            self.notes_text.delete('1.0', tk.END)
    
    def restore_notes_placeholder(self, event):
        """Restaura placeholder das observações"""
        if not self.notes_text.get('1.0', tk.END).strip():
            self.notes_text.insert('1.0', "Digite observações adicionais sobre esta transação...")
    
    def save(self):
        """Salva a transação"""
        try:
            # Validar formulário
            if not self.validate_form():
                return

            # Coletar dados do formulário
            data = self.collect_form_data()

            # Usar TransactionManager para salvar
            from src.modules.transaction_manager import TransactionManager
            transaction_manager = TransactionManager(self.db_manager)

            if self.transaction_id:
                # Atualizar transação existente
                transaction_manager.update_transaction(
                    self.transaction_id,
                    self.user_id,
                    **data
                )
                messagebox.showinfo("Sucesso", "Transação atualizada com sucesso!")
            else:
                # Verificar se é parcelado
                installments = data.get('installments', 1)

                if installments > 1:
                    # Criar transações parceladas automaticamente
                    success = transaction_manager.create_installment_transactions(
                        self.user_id,
                        data['wallet_id'],
                        data['category_id'],
                        self.transaction_type,
                        data['amount'],
                        data['description'],
                        data.get('transaction_date'),
                        data.get('due_date'),
                        data.get('is_paid', False),
                        data.get('notes', ''),
                        installments
                    )

                    if success:
                        messagebox.showinfo("Sucesso",
                            f"Transação parcelada criada com sucesso!\n"
                            f"{installments} parcelas de R$ {data['amount']/installments:.2f} cada.")
                    else:
                        messagebox.showerror("Erro", "Erro ao criar transação parcelada!")
                        return
                else:
                    # Criar transação única
                    transaction_manager.create_transaction(
                        self.user_id,
                        data['wallet_id'],
                        data['category_id'],
                        self.transaction_type,
                        data['amount'],
                        data['description'],
                        data.get('transaction_date'),
                        data.get('due_date'),
                        data.get('is_paid', False),
                        data.get('is_recurring', False),
                        data.get('recurring_type'),
                        data.get('recurring_day'),
                        data.get('notes', ''),
                        1,
                        1
                    )
                    messagebox.showinfo("Sucesso", "Transação criada com sucesso!")

            self.result = True
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao salvar transação: {str(e)}")

    def validate_form(self):
        """Valida o formulário"""
        try:
            # Validar descrição
            description = self.description_entry.get().strip()
            if not description:
                messagebox.showerror("Erro", "Descrição é obrigatória")
                self.description_entry.focus()
                return False

            if len(description) > 200:
                messagebox.showerror("Erro", "Descrição deve ter no máximo 200 caracteres")
                self.description_entry.focus()
                return False

            # Validar valor
            amount_text = self.amount_entry.get().replace(',', '.').replace('R$', '').strip()
            try:
                amount = float(amount_text)
                if amount <= 0:
                    raise ValueError()
            except:
                messagebox.showerror("Erro", "Valor deve ser um número positivo")
                self.amount_entry.focus()
                return False

            # Validar carteira
            if not hasattr(self, 'wallet_combo') or self.wallet_combo.current() == -1:
                messagebox.showerror("Erro", "Selecione uma carteira")
                if hasattr(self, 'wallet_combo'):
                    self.wallet_combo.focus()
                return False

            # Validar categoria
            if not hasattr(self, 'category_combo') or self.category_combo.current() == -1:
                messagebox.showerror("Erro", "Selecione uma categoria")
                if hasattr(self, 'category_combo'):
                    self.category_combo.focus()
                return False

            return True

        except Exception as e:
            messagebox.showerror("Erro", f"Erro na validação: {str(e)}")
            return False

    def collect_form_data(self):
        """Coleta dados do formulário"""
        from datetime import datetime, date

        # Dados básicos
        description = self.description_entry.get().strip()
        amount = float(self.amount_entry.get().replace(',', '.').replace('R$', '').strip())

        # IDs de carteira e categoria
        wallet_display = self.wallet_combo.get()
        category_display = self.category_combo.get()

        # Extrair nomes reais (remover ícones)
        wallet_name = wallet_display.replace("💼 ", "") if wallet_display else ""
        category_name = category_display.replace("📈 ", "").replace("📉 ", "") if category_display else ""

        # Buscar IDs no banco
        wallet_query = "SELECT id FROM wallets WHERE user_id = ? AND name = ?"
        wallet_result = self.db_manager.execute_query(wallet_query, (self.user_id, wallet_name))
        wallet_id = wallet_result[0]['id'] if wallet_result else None

        category_query = "SELECT id FROM categories WHERE name = ? AND (user_id = ? OR user_id IS NULL) AND category_type = ? AND is_active = TRUE"
        category_result = self.db_manager.execute_query(category_query, (category_name, self.user_id, self.transaction_type))
        category_id = category_result[0]['id'] if category_result else None

        if not wallet_id:
            raise ValueError("Carteira não encontrada")
        if not category_id:
            raise ValueError("Categoria não encontrada")

        # Data da transação
        transaction_date = date.today().isoformat()
        if hasattr(self, 'date_entry'):
            try:
                date_text = self.date_entry.get()
                if date_text:
                    transaction_date = datetime.strptime(date_text, '%d/%m/%Y').date().isoformat()
            except:
                pass  # Usar data atual se houver erro

        # Data de vencimento
        due_date = None
        if hasattr(self, 'due_date_entry'):
            try:
                due_date_text = self.due_date_entry.get()
                if due_date_text:
                    due_date = datetime.strptime(due_date_text, '%d/%m/%Y').date().isoformat()
            except:
                pass

        # Status de pagamento
        is_paid = False
        if hasattr(self, 'paid_var'):
            is_paid = self.paid_var.get()

        # Observações
        notes = ""
        if hasattr(self, 'notes_text'):
            notes_content = self.notes_text.get('1.0', tk.END).strip()
            if notes_content and notes_content != "Digite observações adicionais sobre esta transação...":
                notes = notes_content

        # Parcelas
        installments = 1
        if hasattr(self, 'installments_entry'):
            try:
                installments_text = self.installments_entry.get().strip()
                if installments_text:
                    installments = int(installments_text)
                    if installments < 1:
                        installments = 1
                    elif installments > 60:
                        installments = 60
            except:
                installments = 1

        return {
            'wallet_id': wallet_id,
            'category_id': category_id,
            'amount': amount,
            'description': description,
            'transaction_date': transaction_date,
            'due_date': due_date,
            'is_paid': is_paid,
            'notes': notes,
            'installments': installments,
            'installment_number': 1
        }
    
    def cancel(self):
        """Cancela a operação"""
        self.result = False
        self.window.destroy()
