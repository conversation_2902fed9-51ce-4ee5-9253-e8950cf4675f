#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Execução via terminal - sem interface gráfica inicial
Versão corrigida com melhorias para veículos
"""

import sys
import os
import time

def main():
    """Executa a aplicação diretamente"""
    print("=" * 70)
    print("           SISTEMA DE GESTAO DE CONTAS")
    print("           Versao 2.0 - Com Gerenciamento de Veiculos")
    print("=" * 70)
    print() # Linha em branco

    print("📋 Funcionalidades disponíveis:")
    print("   ✓ Controle financeiro completo")
    print("   ✓ Gerenciamento de carteiras")
    print("   ✓ Transações (receitas e despesas)")
    print("   ✓ Relatórios financeiros")
    print("   ✓ NOVO: Cadastro de veículos")
    print("   ✓ NOVO: Controle de manutenção (CORRIGIDO)")
    print("   ✓ NOVO: Registro de combustível")
    print("   ✓ NOVO: Cálculo de eficiência (CORRIGIDO)")
    print("   ✓ NOVO: Estatísticas de consumo")
    print()

    print("🔑 Credenciais padrão:")
    print("   Usuário: admin")
    print("   Senha: admin123")
    print()

    # Layout moderno automático
    login_style = "modern"
    print("🌙 Layout moderno selecionado automaticamente")
    print("   • Fundo escuro com gradiente")
    print("   • Elementos decorativos")
    print("   • Card com efeito glassmorphism")

    print()
    print("🚀 Iniciando aplicação...")
    print("-" * 70)
    
    try:
        # Verificar se os módulos existem
        print("📦 Verificando módulos...")
        
        try:
            import tkinter as tk
            print("   ✓ tkinter - OK")
        except ImportError:
            print("   ✗ tkinter - ERRO: Não encontrado")
            return
        
        try:
            import sqlite3
            print("   ✓ sqlite3 - OK")
        except ImportError:
            print("   ✗ sqlite3 - ERRO: Não encontrado")
            return
        
        try:
            from tkcalendar import DateEntry
            print("   ✓ tkcalendar - OK")
        except ImportError:
            print("   ⚠️ tkcalendar - AVISO: Não encontrado (funcionalidade de data limitada)")
            print("     Instale com: pip install tkcalendar")
        
        # Verificar arquivos do projeto
        print("📁 Verificando arquivos...")
        
        arquivos_essenciais = ['main.py', 'src/database.py', 'src/auth.py', 'src/gui/main_window.py', 'src/gui/login_window.py']
        arquivos_veiculos = ['src/gui/vehicle_manager.py', 'src/gui/vehicle_form.py', 'src/gui/maintenance_form.py', 'src/gui/fuel_form.py']
        
        # Verificar arquivos essenciaisa
        for arquivo in arquivos_essenciais:
            if os.path.exists(arquivo):
                print(f"   ✓ {arquivo} - OK")
            else:
                print(f"   ✗ {arquivo} - ERRO: Não encontrado")
                return
        
        # Verificar arquivos de veículos
        veiculos_ok = True
        for arquivo in arquivos_veiculos:
            if os.path.exists(arquivo):
                print(f"   ✓ {arquivo} - OK")
            else:
                print(f"   ⚠️ {arquivo} - AVISO: Não encontrado (funcionalidade de veículos limitada)")
                veiculos_ok = False
        
        if veiculos_ok:
            print("   ✅ Módulo de veículos completo!")
        else:
            print("   ⚠️ Módulo de veículos incompleto - algumas funcionalidades podem não funcionar")
        
        print()
        print("🔄 Inicializando banco de dados...")
        
        # Importar e inicializar
        from src.database import DatabaseManager
        from src.auth import AuthManager
        
        # Tentar importar sistema de atualizações (pode não existir)
        auto_update_manager = None
        try:
            from src.modules.auto_update_manager import AutoUpdateManager
            auto_update_manager = AutoUpdateManager
            print("   ✓ Sistema de atualizações automáticas disponível")
        except ImportError:
            print("   ⚠️ Sistema de atualizações automáticas não disponível")

        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Verificar tabelas de veículos
        try:
            # Testar se as tabelas de veículos existem
            vehicles = db_manager.execute_query("SELECT COUNT(*) as count FROM vehicles")
            maintenance = db_manager.execute_query("SELECT COUNT(*) as count FROM vehicle_maintenance")
            fuel = db_manager.execute_query("SELECT COUNT(*) as count FROM fuel_records")
            
            print(f"   ✓ Tabela vehicles: {vehicles[0]['count']} registros")
            print(f"   ✓ Tabela vehicle_maintenance: {maintenance[0]['count']} registros")
            print(f"   ✓ Tabela fuel_records: {fuel[0]['count']} registros")
            print("   ✅ Banco de dados de veículos OK!")
            
        except Exception as e:
            print(f"   ⚠️ Erro ao verificar tabelas de veículos: {e}")
            print("   ℹ️ As tabelas serão criadas automaticamente quando necessário")

        auth_manager = AuthManager(db_manager)

        # Inicializar sistema de atualizações automáticas se disponível
        auto_update_instance = None
        if auto_update_manager:
            try:
                auto_update_instance = auto_update_manager(db_manager)
                print("   ✓ Sistema de atualizações automáticas inicializado")
            except Exception as e:
                print(f"   ⚠️ Erro ao inicializar atualizações automáticas: {e}")
        
        # Criar usuário admin se não existir
        if not auth_manager.user_exists('admin'):
            auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
            print("   ✓ Usuário admin criado")
        else:
            print("   ✓ Usuário admin já existe")
        
        print()
        print("🖥️  Abrindo interface gráfica...")
        print("   Uma janela deve aparecer na sua tela!")
        print()

        # Criar interface gráfica principal
        root = tk.Tk()
        root.title("Gestao Financeira")
        root.geometry("800x800")

        # Forçar janela a aparecer na frente
        root.lift()
        root.attributes('-topmost', True)
        root.after(1000, lambda: root.attributes('-topmost', False))
        root.focus_force()

        # Centralizar janela
        root.eval('tk::PlaceWindow . center')

        print("   ✓ Janela principal configurada")
        print(f"   📍 Posição: centralizada")
        print(f"   📏 Tamanho: 800x800")
        
        try:
            root.state('zoomed')  # Maximizar (Windows)
        except:
            try:
                root.attributes('-zoomed', True)  # Linux
            except:
                pass  # Se não conseguir maximizar, continua normal
        
        # Forçar janela aparecer
        root.lift()
        root.attributes('-topmost', True)
        root.focus_force()
        
        # Importar janelas
        from src.gui.login_window import LoginWindow
        from src.gui.main_window import MainWindow
        
        # Esconder janela principal inicialmente
        root.withdraw()
        
        def on_login_success(user_data):
            """Callback após login bem-sucedido"""
            print(f"✓ Login realizado: {user_data['full_name']}")

            # Iniciar sistema de atualizações automáticas
            if auto_update_instance:
                try:
                    auto_update_instance.start()
                    print("   ✓ Sistema de atualizações automáticas iniciado")
                except Exception as e:
                    print(f"   ⚠️ Erro ao iniciar atualizações automáticas: {e}")

            # Mostrar janela principal
            root.deiconify()
            root.attributes('-topmost', False)

            # Criar interface principal
            MainWindow(root, db_manager, auth_manager, user_data, on_logout, auto_update_instance)
        
        def on_logout():
            """Callback no logout"""
            print("✓ Logout realizado")

            # Parar sistema de atualizações automáticas
            if auto_update_instance:
                try:
                    auto_update_instance.stop()
                    print("   ✓ Sistema de atualizações automáticas parado")
                except Exception as e:
                    print(f"   ⚠️ Erro ao parar atualizações automáticas: {e}")

            root.withdraw()
            show_login()
        
        def show_login():
            """Mostra tela de login"""
            print("   🔑 Criando janela de login...")
            try:
                login_window = LoginWindow(root, auth_manager, on_login_success, login_style)
                print("   ✅ Janela de login criada!")

                # Forçar janela de login a aparecer
                if hasattr(login_window, 'window'):
                    login_window.window.lift()
                    login_window.window.attributes('-topmost', True)
                    login_window.window.after(1000, lambda: login_window.window.attributes('-topmost', False))
                    login_window.window.focus_force()
                    print("   ✅ Janela de login forçada para frente!")

            except Exception as e:
                print(f"   ❌ Erro ao criar login: {str(e)}")
                import traceback
                traceback.print_exc()

        # Mostrar login
        print("🔑 Iniciando processo de login...")
        show_login()
        
        print("✅ Interface criada com sucesso!")
        print("✅ Procure a janela de login na sua tela")
        print()
        print("💡 Dicas:")
        print("   - Verifique a barra de tarefas")
        print("   - Use Alt+Tab para alternar janelas")
        print("   - A janela pode estar atrás de outras")
        print()
        print("🔧 Correções recentes:")
        print("   ✅ Salvamento de manutenção corrigido")
        print("   ✅ Cálculo de eficiência de combustível corrigido")
        print("   ✅ Conversão de datas melhorada")
        print("   ✅ Interface de veículos otimizada")
        print()
        print("🔄 Aguardando interação do usuário...")
        print("   (Para encerrar, pressione Ctrl+C)")
        
        # Iniciar loop principal
        root.mainloop()
        
        print("\n✅ Aplicação encerrada normalmente")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Aplicação interrompida pelo usuário")
        
    except Exception as e:
        print(f"\n❌ ERRO: {str(e)}")
        print("\n🔍 Detalhes do erro:")
        import traceback
        traceback.print_exc()
        
        print("\n💡 Possíveis soluções:")
        print("   1. Verifique se o Python está instalado corretamente")
        print("   2. Instale as dependências: py -m pip install -r requirements.txt")
        print("   3. Verifique se todos os arquivos estão presentes")
        print("   4. Para problemas de veículos, verifique se tkcalendar está instalado")
        
        input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
