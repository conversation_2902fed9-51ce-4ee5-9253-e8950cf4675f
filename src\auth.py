#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo de autenticação e gerenciamento de usuários
"""

import sqlite3
from datetime import datetime
import re
import hashlib

# Usar hashlib como alternativa ao bcrypt para teste inicial
try:
    import bcrypt
    BCRYPT_AVAILABLE = True
except ImportError:
    BCRYPT_AVAILABLE = False

class AuthManager:
    def __init__(self, db_manager):
        self.db_manager = db_manager
    
    def hash_password(self, password):
        """Gera hash da senha usando bcrypt ou hashlib como fallback"""
        if BCRYPT_AVAILABLE:
            return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        else:
            # Fallback para hashlib (menos seguro, apenas para desenvolvimento)
            import hashlib
            return hashlib.sha256(password.encode('utf-8')).hexdigest()

    def verify_password(self, password, hashed_password):
        """Verifica se a senha está correta"""
        if BCRYPT_AVAILABLE:
            return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
        else:
            # Fallback para hashlib
            import hashlib
            return hashlib.sha256(password.encode('utf-8')).hexdigest() == hashed_password
    
    def validate_email(self, email):
        """Valida formato do email"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def validate_username(self, username):
        """Valida formato do nome de usuário"""
        if len(username) < 3 or len(username) > 50:
            return False
        return re.match(r'^[a-zA-Z0-9_]+$', username) is not None
    
    def validate_password(self, password):
        """Valida força da senha"""
        if len(password) < 6:
            return False, "Senha deve ter pelo menos 6 caracteres"
        return True, "Senha válida"
    
    def user_exists(self, username):
        """Verifica se usuário já existe"""
        query = "SELECT COUNT(*) as count FROM users WHERE username = ?"
        result = self.db_manager.execute_query(query, (username,))
        return result[0]['count'] > 0
    
    def email_exists(self, email):
        """Verifica se email já está em uso"""
        query = "SELECT COUNT(*) as count FROM users WHERE email = ?"
        result = self.db_manager.execute_query(query, (email,))
        return result[0]['count'] > 0
    
    def create_user(self, username, password, email, full_name, is_admin=False):
        """Cria novo usuário"""
        # Validações
        if not self.validate_username(username):
            raise ValueError("Nome de usuário inválido")
        
        if not self.validate_email(email):
            raise ValueError("Email inválido")
        
        is_valid, msg = self.validate_password(password)
        if not is_valid:
            raise ValueError(msg)
        
        if self.user_exists(username):
            raise ValueError("Nome de usuário já existe")
        
        if self.email_exists(email):
            raise ValueError("Email já está em uso")
        
        # Criar usuário
        password_hash = self.hash_password(password)
        
        query = '''
            INSERT INTO users (username, password_hash, email, full_name, is_admin)
            VALUES (?, ?, ?, ?, ?)
        '''
        
        try:
            self.db_manager.execute_query(query, (username, password_hash, email, full_name, is_admin))
            
            # Log da criação do usuário
            self.log_action(None, "USER_CREATED", f"Usuário {username} criado")
            
            return True
        except Exception as e:
            raise Exception(f"Erro ao criar usuário: {str(e)}")
    
    def authenticate_user(self, username, password):
        """Autentica usuário e retorna dados se válido"""
        query = '''
            SELECT id, username, password_hash, email, full_name, is_admin
            FROM users
            WHERE username = ?
        '''

        result = self.db_manager.execute_query(query, (username,))

        if not result:
            return None

        user_data = dict(result[0])

        # Verificar senha
        if not self.verify_password(password, user_data['password_hash']):
            return None

        # Log do login (se a tabela system_logs existir)
        try:
            self.log_action(user_data['id'], "LOGIN", f"Usuário {username} fez login")
        except:
            pass  # Ignorar se não conseguir fazer log

        # Remover hash da senha dos dados retornados
        del user_data['password_hash']

        # Adicionar is_active como True por padrão (compatibilidade)
        user_data['is_active'] = True

        return user_data
    
    def update_last_login(self, user_id):
        """Atualiza timestamp do último login"""
        # Comentado pois a coluna last_login não existe na tabela atual
        # query = "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?"
        # self.db_manager.execute_query(query, (user_id,))
        pass
    
    def change_password(self, user_id, old_password, new_password):
        """Altera senha do usuário"""
        # Verificar senha atual
        query = "SELECT password_hash FROM users WHERE id = ?"
        result = self.db_manager.execute_query(query, (user_id,))
        
        if not result:
            raise ValueError("Usuário não encontrado")
        
        current_hash = result[0]['password_hash']
        
        if not self.verify_password(old_password, current_hash):
            raise ValueError("Senha atual incorreta")
        
        # Validar nova senha
        is_valid, msg = self.validate_password(new_password)
        if not is_valid:
            raise ValueError(msg)
        
        # Atualizar senha
        new_hash = self.hash_password(new_password)
        query = "UPDATE users SET password_hash = ? WHERE id = ?"
        self.db_manager.execute_query(query, (new_hash, user_id))
        
        # Log da alteração
        self.log_action(user_id, "PASSWORD_CHANGED", "Senha alterada")
        
        return True
    
    def get_all_users(self):
        """Retorna todos os usuários (apenas para admin)"""
        query = '''
            SELECT id, username, email, full_name, is_admin, is_active, 
                   created_at, last_login
            FROM users
            ORDER BY created_at DESC
        '''
        return self.db_manager.execute_query(query)
    
    def toggle_user_status(self, user_id, admin_user_id):
        """Ativa/desativa usuário (apenas admin)"""
        query = "UPDATE users SET is_active = NOT is_active WHERE id = ?"
        self.db_manager.execute_query(query, (user_id,))
        
        # Log da ação
        self.log_action(admin_user_id, "USER_STATUS_CHANGED", f"Status do usuário ID {user_id} alterado")
        
        return True
    
    def delete_user(self, user_id, admin_user_id):
        """Remove usuário (apenas admin)"""
        # Verificar se não é o próprio admin
        if user_id == admin_user_id:
            raise ValueError("Não é possível excluir seu próprio usuário")
        
        query = "DELETE FROM users WHERE id = ?"
        self.db_manager.execute_query(query, (user_id,))
        
        # Log da ação
        self.log_action(admin_user_id, "USER_DELETED", f"Usuário ID {user_id} excluído")
        
        return True
    
    def log_action(self, user_id, action, description):
        """Registra ação no log do sistema"""
        query = '''
            INSERT INTO system_logs (user_id, action, description)
            VALUES (?, ?, ?)
        '''
        self.db_manager.execute_query(query, (user_id, action, description))
    
    def get_user_logs(self, user_id=None, limit=100):
        """Retorna logs do sistema"""
        if user_id:
            query = '''
                SELECT sl.*, u.username
                FROM system_logs sl
                LEFT JOIN users u ON sl.user_id = u.id
                WHERE sl.user_id = ?
                ORDER BY sl.created_at DESC
                LIMIT ?
            '''
            return self.db_manager.execute_query(query, (user_id, limit))
        else:
            query = '''
                SELECT sl.*, u.username
                FROM system_logs sl
                LEFT JOIN users u ON sl.user_id = u.id
                ORDER BY sl.created_at DESC
                LIMIT ?
            '''
            return self.db_manager.execute_query(query, (limit,))
