#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demonstração dos diferentes layouts de login
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.login_window import LoginWindow

def demo_layouts():
    """Demonstra os diferentes layouts de login"""
    print("🎨 DEMONSTRAÇÃO DOS LAYOUTS DE LOGIN")
    print("=" * 50)
    
    try:
        # Inicializar sistema
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        print("✅ Sistema inicializado com sucesso!")
        print()
        
        # <PERSON><PERSON>r janela principal (oculta)
        root = tk.Tk()
        root.withdraw()
        
        def on_login_success(user_data):
            """Callback de sucesso (demo)"""
            print(f"✅ Login realizado: {user_data['full_name']}")
            root.quit()
        
        # Menu de escolha
        while True:
            print("🎨 ESCOLHA O LAYOUT PARA DEMONSTRAR:")
            print("   1. 🌙 Moderno (escuro com gradiente)")
            print("   2. ✨ Minimalista (branco e limpo)")
            print("   3. 📋 Clássico (layout original)")
            print("   4. 🔄 Mostrar todos em sequência")
            print("   0. ❌ Sair")
            print()
            
            choice = input("Digite sua escolha (0-4): ").strip()
            
            if choice == "0":
                print("👋 Saindo da demonstração...")
                break
            elif choice == "1":
                print("🌙 Abrindo layout MODERNO...")
                LoginWindow(root, auth_manager, on_login_success, "modern")
                root.mainloop()
            elif choice == "2":
                print("✨ Abrindo layout MINIMALISTA...")
                LoginWindow(root, auth_manager, on_login_success, "minimal")
                root.mainloop()
            elif choice == "3":
                print("📋 Abrindo layout CLÁSSICO...")
                LoginWindow(root, auth_manager, on_login_success, "classic")
                root.mainloop()
            elif choice == "4":
                print("🔄 Mostrando todos os layouts em sequência...")
                
                layouts = [
                    ("🌙 MODERNO", "modern"),
                    ("✨ MINIMALISTA", "minimal"),
                    ("📋 CLÁSSICO", "classic")
                ]
                
                for name, style in layouts:
                    print(f"   Abrindo {name}...")
                    LoginWindow(root, auth_manager, on_login_success, style)
                    root.mainloop()
                    
                    if input("   Pressione Enter para próximo layout ou 'q' para parar: ").strip().lower() == 'q':
                        break
            else:
                print("❌ Opção inválida! Tente novamente.")
            
            print()
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO: {str(e)}")
        import traceback
        traceback.print_exc()

def show_layout_info():
    """Mostra informações sobre os layouts"""
    print("📋 INFORMAÇÕES DOS LAYOUTS:")
    print()
    
    print("🌙 LAYOUT MODERNO:")
    print("   • Fundo escuro com gradiente")
    print("   • Elementos decorativos (círculos)")
    print("   • Card com efeito glassmorphism")
    print("   • Campos com bordas coloridas")
    print("   • Ícones grandes e modernos")
    print("   • Cores: Azul (#4299e1) e cinza escuro")
    print()
    
    print("✨ LAYOUT MINIMALISTA:")
    print("   • Fundo branco limpo")
    print("   • Design super clean")
    print("   • Campos com bordas sutis")
    print("   • Tipografia Helvetica")
    print("   • Cores: Azul (#2563eb) e cinza claro")
    print("   • Foco na simplicidade")
    print()
    
    print("📋 LAYOUT CLÁSSICO:")
    print("   • Design original do sistema")
    print("   • Fundo cinza claro")
    print("   • Card com borda sólida")
    print("   • Botões coloridos tradicionais")
    print("   • Tipografia Arial")
    print("   • Layout familiar e funcional")
    print()

if __name__ == "__main__":
    print("🎨 DEMO DE LAYOUTS DE LOGIN")
    print("=" * 40)
    print()
    
    choice = input("Deseja ver (1) informações dos layouts ou (2) demonstração? [1/2]: ").strip()
    
    if choice == "1":
        show_layout_info()
    else:
        demo_layouts()
