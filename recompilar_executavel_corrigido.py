#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para recompilar executável corrigido forçando remoção de arquivos
"""

import os
import sys
import subprocess
import time
import shutil
from datetime import datetime

def forcar_remocao_arquivos():
    """Força remoção de arquivos antigos"""
    print("FORCANDO REMOCAO DE ARQUIVOS ANTIGOS")
    print("-" * 40)
    
    arquivos_para_remover = [
        "dist/GestaoFinanceira_CORRIGIDO_v2.1.exe",
        "dist/GestaoFinanceira_CORRIGIDO_v2.1_DEBUG.exe"
    ]
    
    for arquivo in arquivos_para_remover:
        if os.path.exists(arquivo):
            try:
                # Tentar remover normalmente
                os.remove(arquivo)
                print(f"   [OK] {arquivo} removido")
            except PermissionError:
                try:
                    # Tentar com atributos
                    os.chmod(arquivo, 0o777)
                    os.remove(arquivo)
                    print(f"   [OK] {arquivo} removido (forcado)")
                except:
                    print(f"   [AVISO] Nao foi possivel remover {arquivo}")
                    print("   Tentando renomear...")
                    try:
                        novo_nome = arquivo + ".old"
                        os.rename(arquivo, novo_nome)
                        print(f"   [OK] {arquivo} renomeado para .old")
                    except:
                        print(f"   [ERRO] Nao foi possivel processar {arquivo}")
        else:
            print(f"   [INFO] {arquivo} nao existe")

def compilar_executavel_simples():
    """Compila executável usando comando simples"""
    print("\nCOMPILANDO EXECUTAVEL SIMPLES")
    print("-" * 35)
    
    try:
        # Comando mais simples
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--icon=assets/gestao_financeira.ico",
            "--name=GestaoFinanceira_FINAL",
            "--distpath=dist",
            "--workpath=build_final",
            "--specpath=.",
            "--add-data=src;src",
            "--add-data=config;config", 
            "--add-data=assets;assets",
            "--add-data=data;data",
            "executar_terminal_sem_emoji.py"
        ]
        
        print("Iniciando compilacao simples...")
        print("   Aguarde alguns minutos...")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        compile_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"[OK] Compilacao concluida em {compile_time:.1f} segundos!")
            return True
        else:
            print("[ERRO] Erro na compilacao!")
            print("DETALHES:")
            print(result.stderr[-800:])
            return False
            
    except Exception as e:
        print(f"[ERRO] Erro inesperado: {e}")
        return False

def compilar_executavel_debug():
    """Compila versão debug"""
    print("\nCOMPILANDO VERSAO DEBUG")
    print("-" * 30)
    
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--console",
            "--icon=assets/gestao_financeira.ico",
            "--name=GestaoFinanceira_FINAL_DEBUG",
            "--distpath=dist",
            "--workpath=build_debug_final",
            "--specpath=.",
            "--add-data=src;src",
            "--add-data=config;config",
            "--add-data=assets;assets", 
            "--add-data=data;data",
            "executar_terminal_sem_emoji.py"
        ]
        
        print("Compilando versao debug...")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        compile_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"[OK] Debug compilado em {compile_time:.1f} segundos!")
            return True
        else:
            print("[ERRO] Erro na compilacao debug!")
            return False
            
    except Exception as e:
        print(f"[ERRO] Erro no debug: {e}")
        return False

def verificar_executaveis():
    """Verifica executáveis gerados"""
    print("\nVERIFICANDO EXECUTAVEIS")
    print("-" * 25)
    
    executaveis = [
        "dist/GestaoFinanceira_FINAL.exe",
        "dist/GestaoFinanceira_FINAL_DEBUG.exe"
    ]
    
    executaveis_ok = []
    
    for exe in executaveis:
        if os.path.exists(exe):
            size = os.path.getsize(exe) / (1024*1024)
            print(f"   [OK] {os.path.basename(exe)} ({size:.1f} MB)")
            executaveis_ok.append(exe)
        else:
            print(f"   [ERRO] {os.path.basename(exe)} - Nao encontrado")
    
    return executaveis_ok

def testar_executavel(exe_path):
    """Testa executável"""
    print(f"\nTESTANDO: {os.path.basename(exe_path)}")
    print("-" * 30)
    
    try:
        print("   Iniciando teste...")
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        time.sleep(3)
        
        if process.poll() is None:
            print("   [OK] Executavel iniciou corretamente!")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            return True
        else:
            stdout, stderr = process.communicate()
            if stderr:
                print(f"   [ERRO] {stderr.decode()[:200]}")
            return False
            
    except Exception as e:
        print(f"   [ERRO] Erro no teste: {e}")
        return False

def main():
    """Função principal"""
    print("=" * 60)
    print("RECOMPILADOR DE EXECUTAVEL CORRIGIDO")
    print("=" * 60)
    print(f"Data: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    try:
        # Forçar remoção de arquivos antigos
        forcar_remocao_arquivos()
        
        # Compilar executável principal
        if not compilar_executavel_simples():
            print("\n[ERRO] Falha na compilacao principal!")
            return False
        
        # Compilar versão debug
        compilar_executavel_debug()
        
        # Verificar executáveis
        executaveis = verificar_executaveis()
        
        if not executaveis:
            print("\n[ERRO] Nenhum executavel foi gerado!")
            return False
        
        # Testar executável principal
        if executaveis:
            testar_executavel(executaveis[0])
        
        print("\n" + "=" * 60)
        print("RECOMPILACAO CONCLUIDA!")
        print("=" * 60)
        
        print("\nARQUIVOS GERADOS:")
        for exe in executaveis:
            size = os.path.getsize(exe) / (1024*1024)
            print(f"   {os.path.basename(exe)} ({size:.1f} MB)")
        
        print("\nCOMO USAR:")
        print("   1. Execute: dist/GestaoFinanceira_FINAL.exe")
        print("   2. Login: admin / admin123")
        print("   3. Se houver problemas, use a versao DEBUG")
        
        print("\nCORRECOES APLICADAS:")
        print("   * Verificacao de arquivos corrigida")
        print("   * Paths para executavel ajustados")
        print("   * Emojis Unicode removidos")
        print("   * Encoding Windows compativel")
        
        return True
        
    except Exception as e:
        print(f"\n[ERRO] Erro inesperado: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print("\n[OK] Recompilacao bem-sucedida!")
        else:
            print("\n[ERRO] Recompilacao falhou!")
        
        input("\nPressione Enter para sair...")
        
    except KeyboardInterrupt:
        print("\nInterrompido pelo usuario")
    except Exception as e:
        print(f"\nErro: {e}")
