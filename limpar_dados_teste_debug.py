#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para limpar dados automáticos de teste - Versão com debug
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager

def limpar_dados_teste_debug():
    """Remove dados automáticos de teste do banco de dados - com debug"""
    print("🧹 LIMPEZA DE DADOS DE TESTE (DEBUG)")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        print("🔄 Inicializando banco de dados...")
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("✅ Banco de dados inicializado")
        
        print("🔄 Inicializando autenticação...")
        auth_manager = AuthManager(db_manager)
        print("✅ Autenticação inicializada")
        
        # Fazer login
        print("🔐 Fazendo login...")
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        user_id = user_data['id']
        
        # Verificar dados de teste existentes
        print("\n🔍 VERIFICANDO DADOS DE TESTE...")
        
        # Contar transações de teste
        print("   📊 Contando transações...")
        result = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM transactions 
            WHERE user_id = ? AND (
                description LIKE '%teste%' OR 
                description LIKE '%Teste%' OR
                description LIKE '%TESTE%' OR
                description LIKE '%Compra à vista%' OR
                description LIKE '%Compra parcelada%' OR
                description LIKE '%Compra em 12x%' OR
                description LIKE '%Teste 15 parcelas%'
            )
        """, (user_id,))
        test_transactions = result[0]['count'] if result else 0
        print(f"   ✅ Transações de teste: {test_transactions}")
        
        # Contar categorias de teste
        print("   🏷️  Contando categorias...")
        result = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM categories 
            WHERE user_id = ? AND (
                name LIKE '%teste%' OR 
                name LIKE '%Teste%' OR
                name LIKE '%TESTE%' OR
                description LIKE '%teste%'
            )
        """, (user_id,))
        test_categories = result[0]['count'] if result else 0
        print(f"   ✅ Categorias de teste: {test_categories}")
        
        # Contar carteiras de teste
        print("   💳 Contando carteiras...")
        result = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM wallets 
            WHERE user_id = ? AND (
                name LIKE '%teste%' OR 
                name LIKE '%Teste%' OR
                name LIKE '%TESTE%'
            )
        """, (user_id,))
        test_wallets = result[0]['count'] if result else 0
        print(f"   ✅ Carteiras de teste: {test_wallets}")
        
        print(f"\n📊 RESUMO:")
        print(f"   📊 Transações de teste: {test_transactions}")
        print(f"   🏷️  Categorias de teste: {test_categories}")
        print(f"   💳 Carteiras de teste: {test_wallets}")
        
        if test_transactions == 0 and test_categories == 0 and test_wallets == 0:
            print("\n✅ NENHUM DADO DE TESTE ENCONTRADO!")
            print("   Não há nada para limpar.")
            input("\nPressione Enter para sair...")
            return
        
        # Criar interface para confirmação
        print("\n🖥️  CRIANDO INTERFACE GRÁFICA...")
        
        try:
            root = tk.Tk()
            print("   ✅ Janela Tkinter criada")
            
            root.title("Limpeza de Dados de Teste - DEBUG")
            root.geometry("700x600")
            root.configure(bg='#f0f0f0')
            print("   ✅ Configurações básicas aplicadas")
            
            # Forçar janela aparecer na frente
            root.lift()
            root.attributes('-topmost', True)
            root.focus_force()
            print("   ✅ Janela forçada para frente")
            
            # Centralizar janela na tela
            root.update_idletasks()
            width = 700
            height = 600
            x = (root.winfo_screenwidth() // 2) - (width // 2)
            y = (root.winfo_screenheight() // 2) - (height // 2)
            root.geometry(f'{width}x{height}+{x}+{y}')
            print(f"   ✅ Janela centralizada em {x},{y}")
            
            # Frame principal
            main_frame = tk.Frame(root, bg='#f0f0f0', padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)
            print("   ✅ Frame principal criado")
            
            # Título
            title_label = tk.Label(
                main_frame,
                text="🧹 Limpeza de Dados de Teste (DEBUG)",
                font=("Arial", 16, "bold"),
                bg='#f0f0f0',
                fg='#2c3e50'
            )
            title_label.pack(pady=(0, 20))
            print("   ✅ Título adicionado")
            
            # Informações
            info_text = tk.Text(
                main_frame,
                height=20,
                width=80,
                font=("Consolas", 10),
                bg='white',
                relief=tk.SOLID,
                bd=1
            )
            info_text.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
            print("   ✅ Área de texto criada")
            
            # Adicionar informações
            info_text.insert(tk.END, "🔍 DADOS DE TESTE ENCONTRADOS:\n\n")
            info_text.insert(tk.END, f"📊 Transações de teste: {test_transactions}\n")
            info_text.insert(tk.END, f"🏷️  Categorias de teste: {test_categories}\n")
            info_text.insert(tk.END, f"💳 Carteiras de teste: {test_wallets}\n\n")
            
            if test_transactions > 0:
                info_text.insert(tk.END, "📋 TRANSAÇÕES QUE SERÃO REMOVIDAS:\n")
                
                # Listar transações de teste
                test_trans_details = db_manager.execute_query("""
                    SELECT description, amount, transaction_date, installments, installment_number
                    FROM transactions 
                    WHERE user_id = ? AND (
                        description LIKE '%teste%' OR 
                        description LIKE '%Teste%' OR
                        description LIKE '%TESTE%' OR
                        description LIKE '%Compra à vista%' OR
                        description LIKE '%Compra parcelada%' OR
                        description LIKE '%Compra em 12x%' OR
                        description LIKE '%Teste 15 parcelas%'
                    )
                    ORDER BY description, installment_number
                    LIMIT 50
                """, (user_id,))
                
                for trans in test_trans_details:
                    if trans['installments'] > 1:
                        info_text.insert(tk.END, 
                            f"   • {trans['description']} - R$ {trans['amount']:.2f} "
                            f"({trans['installment_number']}/{trans['installments']})\n")
                    else:
                        info_text.insert(tk.END, 
                            f"   • {trans['description']} - R$ {trans['amount']:.2f}\n")
            
            info_text.insert(tk.END, "\n⚠️  ATENÇÃO: Esta ação não pode ser desfeita!\n")
            info_text.insert(tk.END, "✅ Dados reais do usuário serão preservados.\n")
            
            # Desabilitar edição
            info_text.configure(state=tk.DISABLED)
            print("   ✅ Informações adicionadas")
            
            # Função para executar limpeza
            def executar_limpeza():
                try:
                    if messagebox.askyesno(
                        "Confirmar Limpeza",
                        f"Tem certeza que deseja remover:\n\n"
                        f"• {test_transactions} transações de teste\n"
                        f"• {test_categories} categorias de teste\n"
                        f"• {test_wallets} carteiras de teste\n\n"
                        f"Esta ação não pode ser desfeita!"
                    ):
                        print("\n🧹 EXECUTANDO LIMPEZA...")
                        
                        # Remover transações de teste
                        if test_transactions > 0:
                            db_manager.execute_query("""
                                DELETE FROM transactions 
                                WHERE user_id = ? AND (
                                    description LIKE '%teste%' OR 
                                    description LIKE '%Teste%' OR
                                    description LIKE '%TESTE%' OR
                                    description LIKE '%Compra à vista%' OR
                                    description LIKE '%Compra parcelada%' OR
                                    description LIKE '%Compra em 12x%' OR
                                    description LIKE '%Teste 15 parcelas%'
                                )
                            """, (user_id,))
                            print(f"   ✅ {test_transactions} transações de teste removidas")
                        
                        messagebox.showinfo(
                            "Limpeza Concluída",
                            f"Limpeza concluída com sucesso!\n\n"
                            f"• {test_transactions} transações removidas\n\n"
                            f"Reinicie a aplicação para ver as mudanças."
                        )
                        
                        print("\n✅ LIMPEZA CONCLUÍDA COM SUCESSO!")
                        root.destroy()
                    
                except Exception as e:
                    messagebox.showerror("Erro", f"Erro durante a limpeza: {str(e)}")
                    print(f"❌ Erro durante limpeza: {str(e)}")
            
            # Botões
            button_frame = tk.Frame(main_frame, bg='#f0f0f0')
            button_frame.pack(fill=tk.X)
            
            clean_btn = tk.Button(
                button_frame,
                text="🧹 Executar Limpeza",
                command=executar_limpeza,
                font=("Arial", 12, "bold"),
                bg='#e74c3c',
                fg='white',
                padx=20,
                pady=10,
                cursor='hand2'
            )
            clean_btn.pack(side=tk.LEFT, padx=(0, 10))
            
            cancel_btn = tk.Button(
                button_frame,
                text="❌ Cancelar",
                command=root.destroy,
                font=("Arial", 12, "bold"),
                bg='#95a5a6',
                fg='white',
                padx=20,
                pady=10,
                cursor='hand2'
            )
            cancel_btn.pack(side=tk.RIGHT)
            print("   ✅ Botões criados")
            
            # Remover topmost após 2 segundos
            root.after(2000, lambda: root.attributes('-topmost', False))
            
            print("   ✅ Interface completa criada")
            print("   🖥️  Iniciando loop principal...")
            
            # Executar interface
            root.mainloop()
            print("   ✅ Interface encerrada")
            
        except Exception as gui_error:
            print(f"❌ ERRO NA INTERFACE GRÁFICA: {str(gui_error)}")
            import traceback
            traceback.print_exc()
            
            # Fallback para interface de texto
            print("\n🔄 USANDO INTERFACE DE TEXTO...")
            resposta = input(f"\nDeseja remover {test_transactions} transações e {test_categories} categorias de teste? (s/n): ")
            
            if resposta.lower() in ['s', 'sim', 'y', 'yes']:
                print("🧹 EXECUTANDO LIMPEZA...")
                
                if test_transactions > 0:
                    db_manager.execute_query("""
                        DELETE FROM transactions 
                        WHERE user_id = ? AND (
                            description LIKE '%teste%' OR 
                            description LIKE '%Teste%' OR
                            description LIKE '%TESTE%' OR
                            description LIKE '%Compra à vista%' OR
                            description LIKE '%Compra parcelada%' OR
                            description LIKE '%Compra em 12x%' OR
                            description LIKE '%Teste 15 parcelas%'
                        )
                    """, (user_id,))
                    print(f"   ✅ {test_transactions} transações de teste removidas")
                
                print("\n✅ LIMPEZA CONCLUÍDA COM SUCESSO!")
            else:
                print("❌ Limpeza cancelada")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    limpar_dados_teste_debug()
