#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug específico da interface de alertas
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager

def debug_interface_alertas():
    """Debug específico da interface de alertas"""
    print("🔍 DEBUG DA INTERFACE DE ALERTAS")
    print("=" * 45)
    
    try:
        # Inicializar sistema
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        user_id = user_data['id']
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # 1. Buscar alertas do banco
        print(f"\n📊 BUSCANDO ALERTAS DO BANCO...")
        maintenance_alerts = db_manager.get_maintenance_alerts(user_id, 60)
        overdue_alerts = db_manager.get_overdue_maintenance(user_id)
        
        print(f"   🔧 Alertas de manutenção: {len(maintenance_alerts)}")
        print(f"   🚨 Manutenções vencidas: {len(overdue_alerts)}")
        
        # Combinar alertas (igual ao código da interface)
        all_alerts = list(maintenance_alerts) + list(overdue_alerts)
        print(f"   📋 Total combinado: {len(all_alerts)}")
        
        if not all_alerts:
            print("   ❌ Nenhum alerta encontrado!")
            return
        
        # 2. Simular processamento da interface
        print(f"\n🖥️  SIMULANDO PROCESSAMENTO DA INTERFACE...")
        
        processed_alerts = []
        
        for i, alert in enumerate(all_alerts, 1):
            print(f"\n   {i}. Processando alerta:")
            print(f"      Raw data: {alert}")
            
            try:
                vehicle_name = f"{alert['brand']} {alert['model']}"
                maintenance_type = alert['maintenance_type'] or "Manutenção"
                description = alert['description'] or "Sem descrição"
                
                print(f"      Veículo: {vehicle_name}")
                print(f"      Tipo: {maintenance_type}")
                print(f"      Descrição: {description}")
                print(f"      Alert type: {alert.get('alert_type', 'N/A')}")
                
                # Determinar status e situação (igual ao código da interface)
                if hasattr(alert, 'days_overdue') and alert.get('days_overdue', 0) > 0:
                    status = "🔴 VENCIDA"
                    situacao = f"Atrasada {int(alert['days_overdue'])} dias"
                    date_km = alert.get('next_service_date', 'N/A')
                    print(f"      ✅ Detectado como VENCIDA por days_overdue")
                elif alert.get('alert_type') == 'date':
                    days_until = int(alert['days_until'])
                    if days_until <= 0:
                        status = "🔴 VENCIDA"
                        situacao = "Venceu hoje"
                    elif days_until <= 7:
                        status = "🟡 PRÓXIMA"
                        situacao = f"Em {days_until} dias"
                    else:
                        status = "🟢 PROGRAMADA"
                        situacao = f"Em {days_until} dias"
                    date_km = alert['next_service_date']
                    print(f"      ✅ Detectado como alerta por DATA: {status}")
                elif alert.get('alert_type') == 'mileage':
                    km_remaining = int(alert['km_remaining'])
                    if km_remaining <= 0:
                        status = "🔴 VENCIDA"
                        situacao = f"Passou {abs(km_remaining)} km"
                    elif km_remaining <= 1000:
                        status = "🟡 PRÓXIMA"
                        situacao = f"Faltam {km_remaining} km"
                    else:
                        status = "🟢 PROGRAMADA"
                        situacao = f"Faltam {km_remaining} km"
                    date_km = f"{alert['next_service_mileage']} km"
                    print(f"      ✅ Detectado como alerta por QUILOMETRAGEM: {status}")
                else:
                    status = "❓ DESCONHECIDO"
                    situacao = "Tipo não identificado"
                    date_km = "N/A"
                    print(f"      ❌ TIPO NÃO IDENTIFICADO!")
                    print(f"         alert_type: {alert.get('alert_type')}")
                    print(f"         days_overdue: {alert.get('days_overdue')}")
                
                processed_alert = {
                    'status': status,
                    'vehicle_name': vehicle_name,
                    'maintenance_type': maintenance_type,
                    'description': description,
                    'date_km': date_km,
                    'situacao': situacao
                }
                
                processed_alerts.append(processed_alert)
                
                print(f"      📋 Resultado processado:")
                print(f"         Status: {status}")
                print(f"         Situação: {situacao}")
                print(f"         Data/KM: {date_km}")
                
            except Exception as e:
                print(f"      ❌ Erro ao processar: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # 3. Criar interface de teste
        print(f"\n🖥️  CRIANDO INTERFACE DE TESTE...")
        
        root = tk.Tk()
        root.title("Debug - Alertas de Manutenção")
        root.geometry("1000x600")
        
        # Frame principal
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Título
        ttk.Label(main_frame, text="🔍 Debug - Alertas de Manutenção",
                 font=('Arial', 16, 'bold')).pack(pady=(0, 10))
        
        # Info
        info_text = f"Encontrados {len(processed_alerts)} alertas processados"
        ttk.Label(main_frame, text=info_text,
                 font=('Arial', 12)).pack(pady=(0, 10))
        
        # Treeview
        columns = ('Status', 'Veículo', 'Tipo', 'Descrição', 'Data/KM', 'Situação')
        tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=15)
        
        # Configurar colunas
        column_widths = {'Status': 120, 'Veículo': 150, 'Tipo': 120, 'Descrição': 200, 'Data/KM': 100, 'Situação': 150}
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=column_widths.get(col, 100))
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Inserir dados processados
        print(f"\n📋 INSERINDO DADOS NA ÁRVORE...")
        for alert in processed_alerts:
            values = (
                alert['status'],
                alert['vehicle_name'],
                alert['maintenance_type'],
                alert['description'],
                alert['date_km'],
                alert['situacao']
            )
            tree.insert('', 'end', values=values)
            print(f"   ✅ Inserido: {alert['status']} - {alert['vehicle_name']}")
        
        # Botão fechar
        ttk.Button(main_frame, text="Fechar",
                  command=root.quit).pack(pady=(10, 0))
        
        print(f"\n✅ Interface criada com {len(processed_alerts)} alertas!")
        print(f"💡 Verifique se os alertas aparecem na janela")
        print(f"🔍 Procure especialmente por:")
        print(f"   • 🔴 VENCIDA (por quilometragem)")
        print(f"   • 🟡 PRÓXIMA (por data)")
        
        # Executar
        root.mainloop()
        root.destroy()
        
        print(f"\n✅ Debug da interface concluído!")
        
    except Exception as e:
        print(f"❌ ERRO: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_interface_alertas()
