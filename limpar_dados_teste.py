#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para limpar dados automáticos de teste
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager

def limpar_dados_teste():
    """Remove dados automáticos de teste do banco de dados"""
    print("🧹 LIMPEZA DE DADOS DE TESTE")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        user_id = user_data['id']
        
        # Criar interface para confirmação
        root = tk.Tk()
        root.title("Limpeza de Dados de Teste")
        root.geometry("600x500")
        root.configure(bg='#f0f0f0')

        # Forçar janela aparecer na frente
        root.lift()
        root.attributes('-topmost', True)
        root.focus_force()

        # Centralizar janela na tela
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry(f'{width}x{height}+{x}+{y}')

        # Remover topmost após aparecer
        root.after(1000, lambda: root.attributes('-topmost', False))
        
        # Frame principal
        main_frame = tk.Frame(root, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title_label = tk.Label(
            main_frame,
            text="🧹 Limpeza de Dados de Teste",
            font=("Arial", 16, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # Verificar dados de teste existentes
        print("\n🔍 VERIFICANDO DADOS DE TESTE...")
        
        # Contar transações de teste
        result = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM transactions
            WHERE user_id = ? AND (
                description LIKE '%teste%' OR
                description LIKE '%Teste%' OR
                description LIKE '%TESTE%' OR
                description LIKE '%Compra à vista%' OR
                description LIKE '%Compra parcelada%' OR
                description LIKE '%Compra em 12x%' OR
                description LIKE '%Teste 15 parcelas%'
            )
        """, (user_id,))
        test_transactions = result[0]['count'] if result else 0

        # Contar categorias de teste
        result = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM categories
            WHERE user_id = ? AND (
                name LIKE '%teste%' OR
                name LIKE '%Teste%' OR
                name LIKE '%TESTE%' OR
                description LIKE '%teste%'
            )
        """, (user_id,))
        test_categories = result[0]['count'] if result else 0

        # Contar carteiras de teste (se houver)
        result = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM wallets
            WHERE user_id = ? AND (
                name LIKE '%teste%' OR
                name LIKE '%Teste%' OR
                name LIKE '%TESTE%'
            )
        """, (user_id,))
        test_wallets = result[0]['count'] if result else 0

        # Contar veículos de teste
        try:
            result = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM vehicles
                WHERE user_id = ? AND (
                    name LIKE '%teste%' OR
                    name LIKE '%Teste%' OR
                    name LIKE '%TESTE%' OR
                    brand LIKE '%teste%' OR
                    model LIKE '%teste%' OR
                    license_plate LIKE '%TEST%' OR
                    license_plate LIKE '%TST%'
                )
            """, (user_id,))
            test_vehicles = result[0]['count'] if result else 0
        except:
            test_vehicles = 0

        # Contar registros de manutenção de teste
        try:
            result = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM vehicle_maintenance vm
                WHERE vm.user_id = ? AND (
                    vm.description LIKE '%teste%' OR
                    vm.description LIKE '%Teste%' OR
                    vm.description LIKE '%TESTE%' OR
                    vm.description LIKE '%test%' OR
                    vm.description LIKE '%Test%' OR
                    vm.description LIKE '%TEST%' OR
                    vm.maintenance_type LIKE '%test%'
                )
            """, (user_id,))
            test_maintenance = result[0]['count'] if result else 0
        except:
            test_maintenance = 0

        # Contar registros de manutenção de veículos de teste (adicional)
        try:
            result = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM vehicle_maintenance vm
                JOIN vehicles v ON vm.vehicle_id = v.id
                WHERE vm.user_id = ? AND (
                    v.name LIKE '%teste%' OR
                    v.name LIKE '%Teste%' OR
                    v.name LIKE '%TESTE%' OR
                    v.brand LIKE '%teste%' OR
                    v.model LIKE '%teste%' OR
                    v.license_plate LIKE '%TEST%' OR
                    v.license_plate LIKE '%TST%'
                )
            """, (user_id,))
            test_maintenance_vehicles = result[0]['count'] if result else 0
            test_maintenance += test_maintenance_vehicles  # Somar aos totais
        except:
            pass

        # Contar registros de combustível de teste
        try:
            result = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM fuel_records fr
                JOIN vehicles v ON fr.vehicle_id = v.id
                WHERE fr.user_id = ? AND (
                    fr.notes LIKE '%teste%' OR
                    fr.notes LIKE '%Teste%' OR
                    fr.notes LIKE '%TESTE%' OR
                    v.name LIKE '%teste%' OR
                    v.name LIKE '%Teste%' OR
                    v.name LIKE '%TESTE%'
                )
            """, (user_id,))
            test_fuel = result[0]['count'] if result else 0
        except:
            test_fuel = 0
        
        print(f"   📊 Transações de teste: {test_transactions}")
        print(f"   🏷️  Categorias de teste: {test_categories}")
        print(f"   💳 Carteiras de teste: {test_wallets}")
        print(f"   🚗 Veículos de teste: {test_vehicles}")
        print(f"   🔧 Manutenções de teste: {test_maintenance}")
        print(f"   ⛽ Combustível de teste: {test_fuel}")

        # Verificar se há dados para limpar
        if (test_transactions == 0 and test_categories == 0 and test_wallets == 0 and
            test_vehicles == 0 and test_maintenance == 0 and test_fuel == 0):
            print("\n✅ NENHUM DADO DE TESTE ENCONTRADO!")
            print("   Não há nada para limpar.")

            # Mostrar mensagem na interface gráfica
            root = tk.Tk()
            root.withdraw()  # Esconder janela principal

            messagebox.showinfo(
                "Limpeza de Dados",
                "✅ Nenhum dado de teste encontrado!\n\n"
                "Não há transações, categorias ou carteiras de teste para limpar."
            )

            root.destroy()
            return

        # Mostrar informações na interface
        print("🖥️  Criando área de informações...")
        info_text = tk.Text(
            main_frame,
            height=15,
            width=70,
            font=("Consolas", 10),
            bg='white',
            relief=tk.SOLID,
            bd=1
        )
        info_text.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        print("✅ Área de informações criada")
        
        # Adicionar informações detalhadas
        info_text.insert(tk.END, "🔍 DADOS DE TESTE ENCONTRADOS:\n\n")
        info_text.insert(tk.END, f"📊 Transações de teste: {test_transactions}\n")
        info_text.insert(tk.END, f"🏷️  Categorias de teste: {test_categories}\n")
        info_text.insert(tk.END, f"💳 Carteiras de teste: {test_wallets}\n")
        info_text.insert(tk.END, f"🚗 Veículos de teste: {test_vehicles}\n")
        info_text.insert(tk.END, f"🔧 Manutenções de teste: {test_maintenance}\n")
        info_text.insert(tk.END, f"⛽ Combustível de teste: {test_fuel}\n\n")
        
        if test_transactions > 0:
            info_text.insert(tk.END, "📋 TRANSAÇÕES QUE SERÃO REMOVIDAS:\n")
            
            # Listar transações de teste
            test_trans_details = db_manager.execute_query("""
                SELECT description, amount, transaction_date, installments, installment_number
                FROM transactions 
                WHERE user_id = ? AND (
                    description LIKE '%teste%' OR 
                    description LIKE '%Teste%' OR
                    description LIKE '%TESTE%' OR
                    description LIKE '%Compra à vista%' OR
                    description LIKE '%Compra parcelada%' OR
                    description LIKE '%Compra em 12x%' OR
                    description LIKE '%Teste 15 parcelas%'
                )
                ORDER BY description, installment_number
                LIMIT 50
            """, (user_id,))
            
            for trans in test_trans_details:
                if trans['installments'] > 1:
                    info_text.insert(tk.END, 
                        f"   • {trans['description']} - R$ {trans['amount']:.2f} "
                        f"({trans['installment_number']}/{trans['installments']})\n")
                else:
                    info_text.insert(tk.END, 
                        f"   • {trans['description']} - R$ {trans['amount']:.2f}\n")
            
            if len(test_trans_details) == 50:
                info_text.insert(tk.END, "   ... (e mais transações)\n")
        
        if test_categories > 0:
            info_text.insert(tk.END, "\n🏷️  CATEGORIAS QUE SERÃO REMOVIDAS:\n")
            
            test_cat_details = db_manager.execute_query("""
                SELECT name, category_type FROM categories 
                WHERE user_id = ? AND (
                    name LIKE '%teste%' OR 
                    name LIKE '%Teste%' OR
                    name LIKE '%TESTE%' OR
                    description LIKE '%teste%'
                )
            """, (user_id,))
            
            for cat in test_cat_details:
                tipo = "Receita" if cat['category_type'] == 'income' else "Despesa"
                info_text.insert(tk.END, f"   • {cat['name']} ({tipo})\n")
        
        info_text.insert(tk.END, "\n⚠️  ATENÇÃO: Esta ação não pode ser desfeita!\n")
        info_text.insert(tk.END, "✅ Dados reais do usuário serão preservados.\n")
        
        # Desabilitar edição
        info_text.configure(state=tk.DISABLED)
        
        # Função para executar limpeza
        def executar_limpeza():
            try:
                if messagebox.askyesno(
                    "Confirmar Limpeza",
                    f"Tem certeza que deseja remover:\n\n"
                    f"• {test_transactions} transações de teste\n"
                    f"• {test_categories} categorias de teste\n"
                    f"• {test_wallets} carteiras de teste\n"
                    f"• {test_vehicles} veículos de teste\n"
                    f"• {test_maintenance} manutenções de teste\n"
                    f"• {test_fuel} registros de combustível de teste\n\n"
                    f"Esta ação não pode ser desfeita!"
                ):
                    print("\n🧹 EXECUTANDO LIMPEZA...")
                    
                    # Remover transações de teste
                    if test_transactions > 0:
                        db_manager.execute_query("""
                            DELETE FROM transactions 
                            WHERE user_id = ? AND (
                                description LIKE '%teste%' OR 
                                description LIKE '%Teste%' OR
                                description LIKE '%TESTE%' OR
                                description LIKE '%Compra à vista%' OR
                                description LIKE '%Compra parcelada%' OR
                                description LIKE '%Compra em 12x%' OR
                                description LIKE '%Teste 15 parcelas%'
                            )
                        """, (user_id,))
                        print(f"   ✅ {test_transactions} transações de teste removidas")
                    
                    # Remover categorias de teste (apenas se não estiverem em uso)
                    if test_categories > 0:
                        # Verificar quais categorias não estão em uso
                        unused_test_categories = db_manager.execute_query("""
                            SELECT c.id, c.name FROM categories c
                            WHERE c.user_id = ? AND (
                                c.name LIKE '%teste%' OR 
                                c.name LIKE '%Teste%' OR
                                c.name LIKE '%TESTE%' OR
                                c.description LIKE '%teste%'
                            ) AND c.id NOT IN (
                                SELECT DISTINCT category_id FROM transactions WHERE user_id = ?
                            )
                        """, (user_id, user_id))
                        
                        for cat in unused_test_categories:
                            db_manager.execute_query("DELETE FROM categories WHERE id = ?", (cat['id'],))
                        
                        print(f"   ✅ {len(unused_test_categories)} categorias de teste removidas")
                        
                        if len(unused_test_categories) < test_categories:
                            remaining = test_categories - len(unused_test_categories)
                            print(f"   ⚠️  {remaining} categorias de teste mantidas (em uso)")
                    
                    # Remover carteiras de teste (apenas se não estiverem em uso)
                    if test_wallets > 0:
                        unused_test_wallets = db_manager.execute_query("""
                            SELECT w.id, w.name FROM wallets w
                            WHERE w.user_id = ? AND (
                                w.name LIKE '%teste%' OR 
                                w.name LIKE '%Teste%' OR
                                w.name LIKE '%TESTE%'
                            ) AND w.id NOT IN (
                                SELECT DISTINCT wallet_id FROM transactions WHERE user_id = ?
                            )
                        """, (user_id, user_id))
                        
                        for wallet in unused_test_wallets:
                            db_manager.execute_query("DELETE FROM wallets WHERE id = ?", (wallet['id'],))
                        
                        print(f"   ✅ {len(unused_test_wallets)} carteiras de teste removidas")

                    # Remover dados de veículos de teste
                    vehicles_removed = 0
                    maintenance_removed = 0
                    fuel_removed = 0

                    if test_vehicles > 0 or test_maintenance > 0 or test_fuel > 0:
                        try:
                            # Buscar veículos de teste
                            test_vehicles_list = db_manager.execute_query("""
                                SELECT id, name FROM vehicles
                                WHERE user_id = ? AND (
                                    name LIKE '%teste%' OR
                                    name LIKE '%Teste%' OR
                                    name LIKE '%TESTE%' OR
                                    brand LIKE '%teste%' OR
                                    model LIKE '%teste%' OR
                                    license_plate LIKE '%TEST%' OR
                                    license_plate LIKE '%TST%'
                                )
                            """, (user_id,))

                            for vehicle in test_vehicles_list:
                                vehicle_id = vehicle['id']

                                # Remover registros de combustível do veículo
                                try:
                                    db_manager.execute_query("DELETE FROM fuel_records WHERE vehicle_id = ?", (vehicle_id,))
                                    fuel_removed += 1
                                except:
                                    pass

                                # Contar manutenções que serão removidas deste veículo
                                try:
                                    count_result = db_manager.execute_query("SELECT COUNT(*) as count FROM vehicle_maintenance WHERE vehicle_id = ?", (vehicle_id,))
                                    vehicle_maintenance_count = count_result[0]['count'] if count_result else 0

                                    # Remover registros de manutenção do veículo
                                    db_manager.execute_query("DELETE FROM vehicle_maintenance WHERE vehicle_id = ?", (vehicle_id,))
                                    maintenance_removed += vehicle_maintenance_count
                                except:
                                    pass

                                # Remover o veículo
                                db_manager.execute_query("DELETE FROM vehicles WHERE id = ?", (vehicle_id,))
                                vehicles_removed += 1

                            if vehicles_removed > 0:
                                print(f"   ✅ {vehicles_removed} veículos de teste removidos")

                        except Exception as e:
                            print(f"   ⚠️  Erro ao remover dados de veículos: {str(e)}")

                        # Remover manutenções de teste independentes (não de veículos de teste)
                        try:
                            standalone_maintenance = db_manager.execute_query("""
                                DELETE FROM vehicle_maintenance
                                WHERE user_id = ? AND (
                                    description LIKE '%teste%' OR
                                    description LIKE '%Teste%' OR
                                    description LIKE '%TESTE%' OR
                                    description LIKE '%test%' OR
                                    description LIKE '%Test%' OR
                                    description LIKE '%TEST%' OR
                                    maintenance_type LIKE '%test%'
                                )
                            """, (user_id,))

                            # Contar quantas foram removidas
                            standalone_count = db_manager.execute_query("SELECT changes() as count")[0]['count']
                            maintenance_removed += standalone_count

                            if standalone_count > 0:
                                print(f"   ✅ {standalone_count} manutenções de teste independentes removidas")

                        except Exception as e:
                            print(f"   ⚠️  Erro ao remover manutenções de teste: {str(e)}")

                        if maintenance_removed > 0:
                            print(f"   ✅ {maintenance_removed} registros de manutenção removidos no total")
                        if fuel_removed > 0:
                            print(f"   ✅ {fuel_removed} registros de combustível removidos")

                    messagebox.showinfo(
                        "Limpeza Concluída",
                        f"Limpeza concluída com sucesso!\n\n"
                        f"• {test_transactions} transações removidas\n"
                        f"• {vehicles_removed} veículos de teste removidos\n"
                        f"• {maintenance_removed} manutenções removidas\n"
                        f"• {fuel_removed} registros de combustível removidos\n"
                        f"• Categorias e carteiras não utilizadas removidas\n\n"
                        f"Reinicie a aplicação para ver as mudanças."
                    )
                    
                    print("\n✅ LIMPEZA CONCLUÍDA COM SUCESSO!")
                    root.destroy()
                
            except Exception as e:
                messagebox.showerror("Erro", f"Erro durante a limpeza: {str(e)}")
                print(f"❌ Erro durante limpeza: {str(e)}")
        
        # Botões
        button_frame = tk.Frame(main_frame, bg='#f0f0f0')
        button_frame.pack(fill=tk.X)
        
        if (test_transactions > 0 or test_categories > 0 or test_wallets > 0 or
            test_vehicles > 0 or test_maintenance > 0 or test_fuel > 0):
            clean_btn = tk.Button(
                button_frame,
                text="🧹 Executar Limpeza",
                command=executar_limpeza,
                font=("Arial", 12, "bold"),
                bg='#e74c3c',
                fg='white',
                padx=20,
                pady=10,
                cursor='hand2'
            )
            clean_btn.pack(side=tk.LEFT, padx=(0, 10))
        else:
            no_data_label = tk.Label(
                button_frame,
                text="✅ Nenhum dado de teste encontrado!",
                font=("Arial", 12, "bold"),
                bg='#f0f0f0',
                fg='#27ae60'
            )
            no_data_label.pack(side=tk.LEFT)
        
        cancel_btn = tk.Button(
            button_frame,
            text="❌ Cancelar",
            command=root.destroy,
            font=("Arial", 12, "bold"),
            bg='#95a5a6',
            fg='white',
            padx=20,
            pady=10,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.RIGHT)
        
        # Executar interface
        print("🖥️  Iniciando interface gráfica...")
        print("   Uma janela deve aparecer na sua tela!")
        root.mainloop()
        print("✅ Interface encerrada")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    limpar_dados_teste()
