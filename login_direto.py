#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Login direto - versão mais simples
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def login_direto():
    """Cria uma tela de login super simples"""
    print("🔑 LOGIN DIRETO")
    print("=" * 30)
    
    # Criar janela principal
    root = tk.Tk()
    root.title("Login - Gestão Financeira")
    root.geometry("400x300")
    root.configure(bg="#f0f0f0")
    
    # Centralizar janela
    root.eval('tk::PlaceWindow . center')
    
    # Forçar para frente
    root.lift()
    root.attributes('-topmost', True)
    root.after(100, lambda: root.attributes('-topmost', False))
    
    # Título
    title = tk.Label(root, text="💰 Gestão Financeira", 
                    font=('Arial', 18, 'bold'),
                    bg="#f0f0f0", fg="#333")
    title.pack(pady=30)
    
    # Frame para campos
    frame = tk.Frame(root, bg="#f0f0f0")
    frame.pack(pady=20)
    
    # Campo usuário
    tk.Label(frame, text="Usuário:", font=('Arial', 12),
            bg="#f0f0f0").pack(anchor='w')
    
    user_entry = tk.Entry(frame, font=('Arial', 12), width=25)
    user_entry.pack(pady=(5, 15), ipady=5)
    user_entry.insert(0, "admin")  # Pré-preencher
    
    # Campo senha
    tk.Label(frame, text="Senha:", font=('Arial', 12),
            bg="#f0f0f0").pack(anchor='w')
    
    pass_entry = tk.Entry(frame, font=('Arial', 12), width=25, show='*')
    pass_entry.pack(pady=(5, 20), ipady=5)
    pass_entry.insert(0, "admin123")  # Pré-preencher
    
    # Resultado
    result_var = tk.StringVar()
    result_label = tk.Label(frame, textvariable=result_var, 
                           font=('Arial', 10), bg="#f0f0f0")
    result_label.pack(pady=10)
    
    def fazer_login():
        """Função de login"""
        usuario = user_entry.get()
        senha = pass_entry.get()
        
        if usuario == "admin" and senha == "admin123":
            result_var.set("✅ Login realizado com sucesso!")
            result_label.config(fg="green")
            
            # Mostrar mensagem de sucesso
            messagebox.showinfo("Sucesso", 
                              f"Login realizado!\n\n"
                              f"Usuário: {usuario}\n"
                              f"Sistema funcionando!")
            
            # Fechar após 2 segundos
            root.after(2000, root.quit)
        else:
            result_var.set("❌ Usuário ou senha incorretos")
            result_label.config(fg="red")
    
    # Botão login
    login_btn = tk.Button(frame, text="ENTRAR", 
                         font=('Arial', 12, 'bold'),
                         bg="#4CAF50", fg="white", 
                         width=20, height=2,
                         command=fazer_login)
    login_btn.pack(pady=10)
    
    # Info
    tk.Label(frame, text="Credenciais já preenchidas", 
            font=('Arial', 9), fg="#666", bg="#f0f0f0").pack(pady=10)
    
    # Focar no botão
    login_btn.focus()
    
    # Bind Enter
    root.bind('<Return>', lambda e: fazer_login())
    
    print("🖥️  Janela de login criada!")
    print("💡 Credenciais já estão preenchidas")
    print("🔑 Clique em ENTRAR ou pressione Enter")
    
    # Executar
    root.mainloop()
    
    print("✅ Teste de login concluído!")

def teste_tkinter():
    """Testa se tkinter está funcionando"""
    print("🧪 TESTE DO TKINTER")
    print("=" * 25)
    
    try:
        root = tk.Tk()
        root.title("Teste Tkinter")
        root.geometry("300x200")
        root.configure(bg="lightblue")
        
        # Centralizar
        root.eval('tk::PlaceWindow . center')
        
        # Forçar para frente
        root.lift()
        root.attributes('-topmost', True)
        root.after(100, lambda: root.attributes('-topmost', False))
        
        # Conteúdo
        tk.Label(root, text="✅ Tkinter funcionando!", 
                font=('Arial', 14, 'bold'),
                bg="lightblue").pack(expand=True)
        
        tk.Button(root, text="Fechar", 
                 command=root.quit,
                 font=('Arial', 12)).pack(pady=20)
        
        print("🖥️  Janela de teste criada!")
        print("🔍 Verifique se a janela apareceu")
        
        root.mainloop()
        root.destroy()
        
        print("✅ Tkinter está funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no tkinter: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔑 TESTE DE LOGIN DIRETO")
    print("=" * 35)
    
    choice = input("Escolha: (1) Teste tkinter ou (2) Login direto [1/2]: ").strip()
    
    if choice == "1":
        teste_tkinter()
    else:
        login_direto()
