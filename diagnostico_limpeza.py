#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnóstico completo da funcionalidade de limpeza de dados de teste
"""

import sys
import os
import traceback
from datetime import datetime, date

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def diagnostico_limpeza():
    """Executa diagnóstico completo da funcionalidade de limpeza"""
    print("🔍 DIAGNÓSTICO DE LIMPEZA DE DADOS DE TESTE")
    print("=" * 60)
    
    try:
        # 1. Verificar importações
        print("📦 VERIFICANDO IMPORTAÇÕES...")
        
        try:
            from database import DatabaseManager
            print("   ✅ DatabaseManager importado")
        except Exception as e:
            print(f"   ❌ Erro ao importar DatabaseManager: {e}")
            return
        
        try:
            from auth import AuthManager
            print("   ✅ AuthManager importado")
        except Exception as e:
            print(f"   ❌ Erro ao importar AuthManager: {e}")
            return
        
        try:
            import tkinter as tk
            from tkinter import messagebox
            print("   ✅ Tkinter importado")
        except Exception as e:
            print(f"   ❌ Erro ao importar Tkinter: {e}")
            return
        
        # 2. Verificar banco de dados
        print("\n🗄️  VERIFICANDO BANCO DE DADOS...")
        
        try:
            db_manager = DatabaseManager()
            db_manager.initialize_database()
            print("   ✅ Banco de dados inicializado")
        except Exception as e:
            print(f"   ❌ Erro ao inicializar banco: {e}")
            return
        
        # 3. Verificar autenticação
        print("\n🔐 VERIFICANDO AUTENTICAÇÃO...")
        
        try:
            auth_manager = AuthManager(db_manager)
            user_data = auth_manager.authenticate_user('admin', 'admin123')
            if user_data:
                print(f"   ✅ Login realizado: {user_data['full_name']}")
                user_id = user_data['id']
            else:
                print("   ❌ Falha no login!")
                return
        except Exception as e:
            print(f"   ❌ Erro na autenticação: {e}")
            return
        
        # 4. Verificar queries de contagem
        print("\n📊 VERIFICANDO QUERIES DE CONTAGEM...")
        
        try:
            # Query de transações
            result = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM transactions 
                WHERE user_id = ? AND (
                    description LIKE '%teste%' OR 
                    description LIKE '%Teste%' OR
                    description LIKE '%TESTE%' OR
                    description LIKE '%Compra à vista%' OR
                    description LIKE '%Compra parcelada%' OR
                    description LIKE '%Compra em 12x%' OR
                    description LIKE '%Teste 15 parcelas%'
                )
            """, (user_id,))
            test_transactions = result[0]['count'] if result else 0
            print(f"   ✅ Query de transações: {test_transactions} encontradas")
            
            # Query de categorias
            result = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM categories 
                WHERE user_id = ? AND (
                    name LIKE '%teste%' OR 
                    name LIKE '%Teste%' OR
                    name LIKE '%TESTE%' OR
                    description LIKE '%teste%'
                )
            """, (user_id,))
            test_categories = result[0]['count'] if result else 0
            print(f"   ✅ Query de categorias: {test_categories} encontradas")
            
            # Query de carteiras
            result = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM wallets 
                WHERE user_id = ? AND (
                    name LIKE '%teste%' OR 
                    name LIKE '%Teste%' OR
                    name LIKE '%TESTE%'
                )
            """, (user_id,))
            test_wallets = result[0]['count'] if result else 0
            print(f"   ✅ Query de carteiras: {test_wallets} encontradas")
            
        except Exception as e:
            print(f"   ❌ Erro nas queries: {e}")
            traceback.print_exc()
            return
        
        # 5. Verificar interface gráfica
        print("\n🖥️  VERIFICANDO INTERFACE GRÁFICA...")
        
        try:
            root = tk.Tk()
            root.withdraw()  # Esconder janela principal
            print("   ✅ Janela Tkinter criada")
            
            # Testar messagebox
            # messagebox.showinfo("Teste", "Teste de messagebox")
            print("   ✅ Messagebox disponível")
            
            root.destroy()
            
        except Exception as e:
            print(f"   ❌ Erro na interface gráfica: {e}")
            return
        
        # 6. Verificar importação da MainWindow
        print("\n🏠 VERIFICANDO MAINWINDOW...")
        
        try:
            from gui.main_window import MainWindow
            print("   ✅ MainWindow importada")
            
            # Verificar se o método existe
            if hasattr(MainWindow, 'show_reset_options'):
                print("   ✅ Método show_reset_options encontrado")
            else:
                print("   ❌ Método show_reset_options NÃO encontrado")
                
        except Exception as e:
            print(f"   ❌ Erro ao importar MainWindow: {e}")
            return
        
        # 7. Testar execução da limpeza (simulação)
        print("\n🧹 TESTANDO LIMPEZA (SIMULAÇÃO)...")
        
        if test_transactions > 0 or test_categories > 0 or test_wallets > 0:
            print(f"   📋 Dados disponíveis para limpeza:")
            print(f"      • Transações: {test_transactions}")
            print(f"      • Categorias: {test_categories}")
            print(f"      • Carteiras: {test_wallets}")
            
            # Simular limpeza (sem executar)
            try:
                # Query de limpeza de transações (apenas teste)
                query_test = """
                    SELECT id, description FROM transactions 
                    WHERE user_id = ? AND (
                        description LIKE '%teste%' OR 
                        description LIKE '%Teste%' OR
                        description LIKE '%TESTE%' OR
                        description LIKE '%Compra à vista%' OR
                        description LIKE '%Compra parcelada%' OR
                        description LIKE '%Compra em 12x%' OR
                        description LIKE '%Teste 15 parcelas%'
                    ) LIMIT 5
                """
                
                test_data = db_manager.execute_query(query_test, (user_id,))
                print(f"   ✅ Query de limpeza testada: {len(test_data)} registros encontrados")
                
                if test_data:
                    print("   📋 Exemplos de dados que seriam removidos:")
                    for item in test_data:
                        print(f"      • ID {item['id']}: {item['description']}")
                
            except Exception as e:
                print(f"   ❌ Erro na query de limpeza: {e}")
                return
        else:
            print("   ℹ️  Nenhum dado de teste encontrado para limpeza")
        
        # 8. Resumo final
        print(f"\n✅ DIAGNÓSTICO CONCLUÍDO COM SUCESSO!")
        print(f"📋 RESUMO:")
        print(f"   • Banco de dados: ✅ Funcionando")
        print(f"   • Autenticação: ✅ Funcionando")
        print(f"   • Queries: ✅ Funcionando")
        print(f"   • Interface gráfica: ✅ Funcionando")
        print(f"   • MainWindow: ✅ Funcionando")
        print(f"   • Dados de teste: {test_transactions + test_categories + test_wallets} itens")
        
        if test_transactions > 0 or test_categories > 0 or test_wallets > 0:
            print(f"\n💡 RECOMENDAÇÃO:")
            print(f"   A funcionalidade de limpeza deve estar funcionando.")
            print(f"   Se ainda há erro, execute:")
            print(f"   1. py limpar_dados_teste.py (script standalone)")
            print(f"   2. Ou acesse via Menu > Administração > Limpeza de Dados")
        else:
            print(f"\n💡 RECOMENDAÇÃO:")
            print(f"   Não há dados de teste para limpar.")
            print(f"   Execute 'py criar_dados_teste.py' para criar dados de teste.")
        
    except Exception as e:
        print(f"\n❌ ERRO GERAL NO DIAGNÓSTICO: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    diagnostico_limpeza()
