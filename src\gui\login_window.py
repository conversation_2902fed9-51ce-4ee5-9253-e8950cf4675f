#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Janela de login do sistema - Design Elegante
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font as tkFont

class LoginWindow:
    def __init__(self, parent, auth_manager, success_callback, layout_style="modern"):
        self.parent = parent
        self.auth_manager = auth_manager
        self.success_callback = success_callback
        self.layout_style = layout_style

        # Criar janela de login
        self.window = tk.Toplevel(parent)
        self.window.title("💰 Gestão Financeira")

        # Definir tamanho baseado no layout
        if layout_style == "modern":
            self.window.geometry("850x850")
        elif layout_style == "minimal":
            self.window.geometry("450x550")
        else:  # classic
            self.window.geometry("500x600")

        self.window.resizable(False, False)

        # Centralizar janela
        self.center_window()

        # Configurar janela
        self.window.transient(parent)
        self.window.grab_set()

        # For<PERSON>r janela para frente
        self.window.lift()
        self.window.attributes('-topmost', True)
        self.window.after(100, lambda: self.window.attributes('-topmost', False))

        # Criar interface baseada no estilo
        self.create_interface()

    def create_interface(self):
        """Cria interface baseada no estilo selecionado"""
        if self.layout_style == "modern":
            self.create_modern_interface()
        elif self.layout_style == "minimal":
            self.create_minimal_interface()
        else:  # classic
            self.create_classic_interface()

        # Focar no campo de usuário
        self.username_entry.focus()

        # Bind Enter para fazer login
        self.window.bind('<Return>', lambda e: self.login())
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_modern_interface(self):
        """Cria interface moderna com gradiente"""
        # Fundo com gradiente simulado
        self.window.configure(bg="#1a202c")

        # Container principal com efeito de profundidade
        main_frame = tk.Frame(self.window, bg="#1a202c")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Adicionar elementos decorativos
        self.create_decorative_elements(main_frame)

        # Centralizar conteúdo
        center_frame = tk.Frame(main_frame, bg="#1a202c")
        center_frame.place(relx=0.5, rely=0.5, anchor='center')

        # Card moderno com sombra
        self.create_modern_card(center_frame)

    def create_decorative_elements(self, parent):
        """Cria elementos decorativos no fundo"""
        # Círculos decorativos
        canvas = tk.Canvas(parent, bg="#1a202c", highlightthickness=0)
        canvas.place(x=0, y=0, relwidth=1, relheight=1)

        # Círculo grande no canto superior direito
        canvas.create_oval(350, -50, 550, 150, fill="#2d3748", outline="")

        # Círculo médio no canto inferior esquerdo
        canvas.create_oval(-50, 400, 150, 600, fill="#4a5568", outline="")

        # Círculo pequeno no meio
        canvas.create_oval(200, 100, 300, 200, fill="#2d3748", outline="")

    def create_modern_card(self, parent):
        """Cria card moderno com efeito glassmorphism"""
        # Frame de sombra (simulando sombra)
        shadow_frame = tk.Frame(parent, bg="#0d1117", width=360, height=580)
        shadow_frame.pack()
        shadow_frame.pack_propagate(False)

        # Card principal com efeito de vidro
        card = tk.Frame(shadow_frame, bg="#2d3748", relief='flat', bd=0)
        card.place(x=5, y=5, width=350, height=570)

        # Container interno
        inner_frame = tk.Frame(card, bg="#2d3748", padx=40, pady=40)
        inner_frame.pack(fill=tk.BOTH, expand=True)

        # Logo/Ícone grande
        logo_frame = tk.Frame(inner_frame, bg="#2d3748")
        logo_frame.pack(pady=(0, 20))

        logo_label = tk.Label(logo_frame, text="💰", font=('Arial', 48),
                             bg="#2d3748", fg="#4299e1")
        logo_label.pack()

        # Título moderno
        title = tk.Label(inner_frame, text="Gestão Financeira",
                        font=('Segoe UI', 24, 'bold'),
                        fg='#ffffff', bg="#2d3748")
        title.pack(pady=(0, 10))

        # Subtítulo
        subtitle = tk.Label(inner_frame, text="Controle suas finanças com elegância",
                           font=('Segoe UI', 10),
                           fg='#a0aec0', bg="#2d3748")
        subtitle.pack(pady=(0, 30))

        # Campos com estilo moderno
        self.create_modern_field(inner_frame, "👤 Usuário", "username_entry", False)
        self.create_modern_field(inner_frame, "🔒 Senha", "password_entry", True)

        # Botão principal moderno
        login_btn = tk.Button(inner_frame, text="ENTRAR",
                             font=('Segoe UI', 12, 'bold'),
                             bg="#a6adb3", fg='white', width=25, height=2,
                             relief='flat', cursor='hand2', command=self.login,
                             activebackground="#8e9397", activeforeground="white")
        login_btn.pack(pady=(20, 15))

        # Link para criar conta
        register_frame = tk.Frame(inner_frame, bg="#2d3748")
        register_frame.pack(pady=(10, 0))

        tk.Label(register_frame, text="Não tem conta?",
                font=('Segoe UI', 9), fg='#a0aec0', bg="#2d3748").pack(side='left')

        register_link = tk.Label(register_frame, text="Criar agora",
                                font=('Segoe UI', 9, 'underline'),
                                fg='#4299e1', bg="#2d3748", cursor='hand2')
        register_link.pack(side='left', padx=(5, 0))
        register_link.bind("<Button-1>", lambda e: self.show_register())

        # Informações de acesso
        info_frame = tk.Frame(inner_frame, bg="#4a5568", relief='flat')
        info_frame.pack(pady=(20, 0), fill='x')

        tk.Label(info_frame, text="💡 Acesso padrão: admin / admin123",
                font=('Segoe UI', 8), fg="#e2e8f0", bg="#4a5568").pack(pady=8)

    def create_modern_field(self, parent, label_text, entry_name, is_password):
        """Cria campo moderno com ícone"""
        field_frame = tk.Frame(parent, bg="#2d3748")
        field_frame.pack(fill='x', pady=(0, 15))

        # Label com ícone
        label = tk.Label(field_frame, text=label_text,
                        font=('Segoe UI', 10, 'bold'),
                        fg='#e2e8f0', bg="#2d3748")
        label.pack(anchor='w', pady=(0, 5))

        # Entry moderno
        entry = tk.Entry(field_frame, font=('Segoe UI', 12),
                        bg="#4a5568", fg="white", relief='flat', bd=0,
                        insertbackground="white", width=25,
                        show='*' if is_password else '')
        entry.pack(ipady=12, fill='x')

        # Adicionar borda inferior
        border = tk.Frame(field_frame, bg="#4299e1", height=2)
        border.pack(fill='x', pady=(2, 0))

        setattr(self, entry_name, entry)

    def create_minimal_interface(self):
        """Cria interface minimalista e limpa"""
        # Fundo branco limpo
        self.window.configure(bg="#ffffff")

        # Container principal
        main_frame = tk.Frame(self.window, bg="#ffffff")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Centralizar conteúdo
        center_frame = tk.Frame(main_frame, bg="#ffffff")
        center_frame.place(relx=0.5, rely=0.5, anchor='center')

        # Card minimalista
        card = tk.Frame(center_frame, bg="#ffffff", padx=50, pady=50)
        card.pack()

        # Logo simples
        logo_label = tk.Label(card, text="💰", font=('Arial', 36),
                             bg="#ffffff", fg="#2563eb")
        logo_label.pack(pady=(0, 10))

        # Título limpo
        title = tk.Label(card, text="Gestão Financeira",
                        font=('Helvetica', 20, 'bold'),
                        fg='#1f2937', bg="#ffffff")
        title.pack(pady=(0, 40))

        # Campos minimalistas
        self.create_minimal_field(card, "Usuário", "username_entry", False)
        self.create_minimal_field(card, "Senha", "password_entry", True)

        # Botão principal limpo
        login_btn = tk.Button(card, text="Entrar",
                             font=('Helvetica', 12, 'bold'),
                             bg='#2563eb', fg='white', width=30, height=2,
                             relief='flat', cursor='hand2', command=self.login,
                             activebackground="#1d4ed8")
        login_btn.pack(pady=(30, 20))

        # Link discreto
        register_link = tk.Label(card, text="Criar nova conta",
                                font=('Helvetica', 10, 'underline'),
                                fg='#6b7280', bg="#ffffff", cursor='hand2')
        register_link.pack(pady=(10, 0))
        register_link.bind("<Button-1>", lambda _: self.show_register())

        # Info discreta
        tk.Label(card, text="admin / admin123",
                font=('Helvetica', 8), fg="#9ca3af", bg="#ffffff").pack(pady=(20, 0))

    def create_minimal_field(self, parent, label_text, entry_name, is_password):
        """Cria campo minimalista"""
        field_frame = tk.Frame(parent, bg="#ffffff")
        field_frame.pack(fill='x', pady=(0, 20))

        # Label simples
        label = tk.Label(field_frame, text=label_text,
                        font=('Helvetica', 11),
                        fg='#374151', bg="#ffffff")
        label.pack(anchor='w', pady=(0, 8))

        # Entry limpo
        entry = tk.Entry(field_frame, font=('Helvetica', 12),
                        bg="#f9fafb", fg="#111827", relief='solid', bd=1,
                        width=30, show='*' if is_password else '')
        entry.pack(ipady=12)

        setattr(self, entry_name, entry)

    def create_classic_interface(self):
        """Cria interface clássica original"""
        # Fundo clássico
        self.window.configure(bg="#F7FAFC")

        # Container principal
        main_frame = tk.Frame(self.window, bg="#F7FAFC")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Centralizar conteúdo
        center_frame = tk.Frame(main_frame, bg="#F7FAFC")
        center_frame.place(relx=0.5, rely=0.5, anchor='center')

        # Card clássico
        card = tk.Frame(center_frame, bg='white', relief='solid', bd=1, padx=40, pady=40)
        card.pack()

        # Título clássico
        title = tk.Label(card, text="💰 Gestão Financeira",
                        font=('Arial', 22, 'bold'),
                        fg='#2d3748', bg='white')
        title.pack(pady=(0, 30))

        # Campos clássicos
        self.create_classic_field(card, "Usuário:", "username_entry", False)
        self.create_classic_field(card, "Senha:", "password_entry", True)

        # Botões clássicos
        login_btn = tk.Button(card, text="Entrar", font=('Arial', 12, 'bold'),
                             bg='#4299e1', fg='white', width=20,
                             relief='flat', cursor='hand2', command=self.login)
        login_btn.pack(pady=(30, 15), ipady=8)

        register_btn = tk.Button(card, text="Criar Nova Conta", font=('Arial', 10),
                                bg='#68d391', fg='white', width=20,
                                relief='flat', cursor='hand2', command=self.show_register)
        register_btn.pack(pady=(0, 20), ipady=6)

        # Informações clássicas
        tk.Label(card, text="Usuário: admin | Senha: admin123",
                font=('Arial', 9), fg="#3B6474", bg='white').pack()

    def create_classic_field(self, parent, label_text, entry_name, is_password):
        """Cria campo clássico"""
        tk.Label(parent, text=label_text, font=('Arial', 12, 'bold'),
                fg='#4a5568', bg='white').pack(anchor='w', pady=(0, 5))

        entry = tk.Entry(parent, font=('Arial', 12), width=20,
                        relief='solid', bd=1, show='*' if is_password else '')
        entry.pack(pady=(0, 20), ipady=8)

        setattr(self, entry_name, entry)

    def login(self):
        """Realiza o login"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()

        if not username or not password:
            messagebox.showerror("Erro", "Por favor, preencha todos os campos")
            return

        try:
            user_data = self.auth_manager.authenticate_user(username, password)

            if user_data:
                # Login bem-sucedido
                self.window.destroy()
                self.success_callback(user_data)
            else:
                messagebox.showerror("Erro", "Usuário ou senha incorretos")
                self.password_entry.delete(0, tk.END)
                self.password_entry.focus()

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao fazer login: {str(e)}")
    
    def show_register(self):
        """Mostra janela de registro"""
        RegisterWindow(self.window, self.auth_manager, self.on_register_success)
    
    def on_register_success(self):
        """Callback executado após registro bem-sucedido"""
        messagebox.showinfo("Sucesso", "Conta criada com sucesso! Faça login para continuar.")

class RegisterWindow:
    def __init__(self, parent, auth_manager, success_callback):
        self.parent = parent
        self.auth_manager = auth_manager
        self.success_callback = success_callback

        # Criar janela de registro simples
        self.window = tk.Toplevel(parent)
        self.window.title("Criar Nova Conta")
        self.window.geometry("400x500")
        self.window.resizable(False, False)
        self.window.configure(bg='#f7fafc')

        # Centralizar janela
        self.center_window()

        # Configurar janela
        self.window.transient(parent)
        self.window.grab_set()

        # Criar interface simples
        self.create_simple_register_widgets()

        # Focar no primeiro campo
        self.full_name_entry.focus()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_simple_register_widgets(self):
        """Cria interface simples de registro"""
        # Frame principal
        main_frame = tk.Frame(self.window, bg='#f7fafc')
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Centralizar conteúdo
        center_frame = tk.Frame(main_frame, bg='#f7fafc')
        center_frame.place(relx=0.5, rely=0.5, anchor='center')

        # Card simples
        card = tk.Frame(center_frame, bg='white', relief='solid', bd=1, padx=30, pady=30)
        card.pack()

        # Título
        title = tk.Label(card, text="📝 Criar Nova Conta",
                        font=('Arial', 18, 'bold'),
                        fg='#2d3748', bg='white')
        title.pack(pady=(0, 20))

        # Campos
        fields = [
            ("Nome Completo:", "full_name_entry"),
            ("Nome de Usuário:", "username_entry"),
            ("Email:", "email_entry"),
            ("Senha:", "password_entry"),
            ("Confirmar Senha:", "confirm_password_entry")
        ]

        for label_text, entry_name in fields:
            tk.Label(card, text=label_text, font=('Arial', 10, 'bold'),
                    fg='#4a5568', bg='white').pack(anchor='w', pady=(0, 3))

            is_password = 'password' in entry_name
            entry = tk.Entry(card, font=('Arial', 10), width=30,
                           relief='solid', bd=1,
                           show='*' if is_password else '')
            entry.pack(pady=(0, 15), ipady=6)
            setattr(self, entry_name, entry)

        # Botões
        button_frame = tk.Frame(card, bg='white')
        button_frame.pack(pady=(10, 0))

        tk.Button(button_frame, text="Cancelar", font=('Arial', 10),
                 bg='#a0aec0', fg='white', width=12, relief='flat',
                 cursor='hand2', command=self.window.destroy).pack(side='left', padx=(0, 10), ipady=6)

        tk.Button(button_frame, text="Criar Conta", font=('Arial', 10, 'bold'),
                 bg='#48bb78', fg='white', width=12, relief='flat',
                 cursor='hand2', command=self.create_account).pack(side='right', ipady=6)
    
    def create_account(self):
        """Cria nova conta de usuário"""
        full_name = self.full_name_entry.get().strip()
        username = self.username_entry.get().strip()
        email = self.email_entry.get().strip()
        password = self.password_entry.get()
        confirm_password = self.confirm_password_entry.get()

        # Validações
        if not all([full_name, username, email, password, confirm_password]):
            messagebox.showerror("Erro", "Por favor, preencha todos os campos")
            return

        if password != confirm_password:
            messagebox.showerror("Erro", "As senhas não coincidem")
            return

        if len(password) < 6:
            messagebox.showerror("Erro", "A senha deve ter pelo menos 6 caracteres")
            return

        try:
            self.auth_manager.create_user(
                username=username,
                password=password,
                email=email,
                full_name=full_name,
                is_admin=False
            )

            messagebox.showinfo("Sucesso", "Conta criada com sucesso!")
            self.window.destroy()
            self.success_callback()

        except Exception as e:
            messagebox.showerror("Erro", str(e))
