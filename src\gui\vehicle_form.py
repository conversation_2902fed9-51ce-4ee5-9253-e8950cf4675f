#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formulário para gerenciamento de veículos
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
try:
    from tkcalendar import DateEntry
    TKCALENDAR_AVAILABLE = True
except ImportError:
    TKCALENDAR_AVAILABLE = False
    print("⚠️ tkcalendar não disponível - usando Entry simples para datas")

class VehicleForm:
    def __init__(self, parent, db_manager, user_id, vehicle_data=None, callback=None):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.vehicle_data = vehicle_data
        self.callback = callback
        self.is_edit_mode = vehicle_data is not None
        
        # Criar janela
        self.window = tk.Toplevel(parent)
        self.window.title("Editar Veículo" if self.is_edit_mode else "Novo Veículo")
        self.window.geometry("600x750")
        self.window.resizable(<PERSON>alse, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        # Centralizar janela
        self.center_window()
        
        # Criar interface
        self.create_widgets()
        
        # Preencher dados se for edição
        if self.is_edit_mode:
            self.populate_fields()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"600x750+{x}+{y}")
    
    def create_widgets(self):
        """Cria os widgets do formulário"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title = ttk.Label(main_frame, text="Editar Veículo" if self.is_edit_mode else "Novo Veículo",
                         font=('Arial', 14, 'bold'))
        title.pack(pady=(0, 20))
        
        # Frame para campos
        fields_frame = ttk.Frame(main_frame)
        fields_frame.pack(fill=tk.BOTH, expand=True)
        
        # Nome do veículo
        ttk.Label(fields_frame, text="Nome/Apelido:*").grid(row=0, column=0, sticky='w', pady=5)
        self.name_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.name_var, width=40).grid(row=0, column=1, sticky='ew', pady=5)
        
        # Marca
        ttk.Label(fields_frame, text="Marca:*").grid(row=1, column=0, sticky='w', pady=5)
        self.brand_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.brand_var, width=40).grid(row=1, column=1, sticky='ew', pady=5)
        
        # Modelo
        ttk.Label(fields_frame, text="Modelo:*").grid(row=2, column=0, sticky='w', pady=5)
        self.model_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.model_var, width=40).grid(row=2, column=1, sticky='ew', pady=5)
        
        # Ano
        ttk.Label(fields_frame, text="Ano:*").grid(row=3, column=0, sticky='w', pady=5)
        self.year_var = tk.StringVar()
        year_entry = ttk.Entry(fields_frame, textvariable=self.year_var, width=40)
        year_entry.grid(row=3, column=1, sticky='ew', pady=5)
        
        # Placa
        ttk.Label(fields_frame, text="Placa:").grid(row=4, column=0, sticky='w', pady=5)
        self.license_plate_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.license_plate_var, width=40).grid(row=4, column=1, sticky='ew', pady=5)
        
        # Cor
        ttk.Label(fields_frame, text="Cor:").grid(row=5, column=0, sticky='w', pady=5)
        self.color_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.color_var, width=40).grid(row=5, column=1, sticky='ew', pady=5)
        
        # Tipo de combustível
        ttk.Label(fields_frame, text="Combustível:").grid(row=6, column=0, sticky='w', pady=5)
        self.fuel_type_var = tk.StringVar(value='gasoline')
        fuel_combo = ttk.Combobox(fields_frame, textvariable=self.fuel_type_var, width=37, state='readonly')
        fuel_combo['values'] = ('gasolina', 'ethanol', 'diesel', 'flex', 'electric')
        fuel_combo.grid(row=6, column=1, sticky='ew', pady=5)
        
        # Motor
        ttk.Label(fields_frame, text="Motor:").grid(row=7, column=0, sticky='w', pady=5)
        self.engine_size_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.engine_size_var, width=40).grid(row=7, column=1, sticky='ew', pady=5)
        
        # Quilometragem
        ttk.Label(fields_frame, text="Quilometragem:").grid(row=8, column=0, sticky='w', pady=5)
        self.mileage_var = tk.StringVar(value='0')
        ttk.Entry(fields_frame, textvariable=self.mileage_var, width=40).grid(row=8, column=1, sticky='ew', pady=5)
        
        # Data de compra
        ttk.Label(fields_frame, text="Data de Compra:").grid(row=9, column=0, sticky='w', pady=5)
        self.purchase_date_var = tk.StringVar()
        try:
            self.purchase_date_entry = DateEntry(fields_frame, textvariable=self.purchase_date_var, 
                                               width=37, background='darkblue',
                                               foreground='white', borderwidth=2, date_pattern='dd/mm/yyyy')
            self.purchase_date_entry.grid(row=9, column=1, sticky='ew', pady=5)
        except:
            # Fallback se DateEntry não estiver disponível
            ttk.Entry(fields_frame, textvariable=self.purchase_date_var, width=40).grid(row=9, column=1, sticky='ew', pady=5)
        
        # Preço de compra
        ttk.Label(fields_frame, text="Preço de Compra:").grid(row=10, column=0, sticky='w', pady=5)
        self.purchase_price_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.purchase_price_var, width=40).grid(row=10, column=1, sticky='ew', pady=5)
        
        # Valor atual
        ttk.Label(fields_frame, text="Valor Atual:").grid(row=11, column=0, sticky='w', pady=5)
        self.current_value_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.current_value_var, width=40).grid(row=11, column=1, sticky='ew', pady=5)
        
        # Seguradora
        ttk.Label(fields_frame, text="Seguradora:").grid(row=12, column=0, sticky='w', pady=5)
        self.insurance_company_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.insurance_company_var, width=40).grid(row=12, column=1, sticky='ew', pady=5)
        
        # Apólice
        ttk.Label(fields_frame, text="Nº Apólice:").grid(row=13, column=0, sticky='w', pady=5)
        self.insurance_policy_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.insurance_policy_var, width=40).grid(row=13, column=1, sticky='ew', pady=5)
        
        # Vencimento do seguro
        ttk.Label(fields_frame, text="Vencimento Seguro:").grid(row=14, column=0, sticky='w', pady=5)
        self.insurance_expiry_var = tk.StringVar()
        try:
            self.insurance_expiry_entry = DateEntry(fields_frame, textvariable=self.insurance_expiry_var,
                                                   width=37, background='darkblue',
                                                   foreground='white', borderwidth=2, date_pattern='dd/mm/yyyy')
            self.insurance_expiry_entry.grid(row=14, column=1, sticky='ew', pady=5)
        except:
            ttk.Entry(fields_frame, textvariable=self.insurance_expiry_var, width=40).grid(row=14, column=1, sticky='ew', pady=5)
        
        # Observações
        ttk.Label(fields_frame, text="Observações:").grid(row=15, column=0, sticky='nw', pady=5)
        self.notes_text = tk.Text(fields_frame, height=4, width=40)
        self.notes_text.grid(row=15, column=1, sticky='ew', pady=5)
        
        # Configurar grid
        fields_frame.columnconfigure(1, weight=1)
        
        # Frame para botões
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(15, 0))
        
        # Botões
        ttk.Button(buttons_frame, text="Cancelar", command=self.cancel, width=12).pack(side=tk.RIGHT, padx=(15, 0))
        ttk.Button(buttons_frame, text="Salvar", command=self.save, width=12).pack(side=tk.RIGHT)
    
    def populate_fields(self):
        """Preenche os campos com dados do veículo para edição"""
        if not self.vehicle_data:
            return
            
        self.name_var.set(self.vehicle_data.get('name', ''))
        self.brand_var.set(self.vehicle_data.get('brand', ''))
        self.model_var.set(self.vehicle_data.get('model', ''))
        self.year_var.set(str(self.vehicle_data.get('year', '')))
        self.license_plate_var.set(self.vehicle_data.get('license_plate', ''))
        self.color_var.set(self.vehicle_data.get('color', ''))
        self.fuel_type_var.set(self.vehicle_data.get('fuel_type', 'gasoline'))
        self.engine_size_var.set(self.vehicle_data.get('engine_size', ''))
        self.mileage_var.set(str(self.vehicle_data.get('mileage', 0)))
        
        if self.vehicle_data.get('purchase_date'):
            self.purchase_date_var.set(self.vehicle_data['purchase_date'])
        
        if self.vehicle_data.get('purchase_price'):
            self.purchase_price_var.set(str(self.vehicle_data['purchase_price']))
            
        if self.vehicle_data.get('current_value'):
            self.current_value_var.set(str(self.vehicle_data['current_value']))
            
        self.insurance_company_var.set(self.vehicle_data.get('insurance_company', ''))
        self.insurance_policy_var.set(self.vehicle_data.get('insurance_policy', ''))
        
        if self.vehicle_data.get('insurance_expiry'):
            self.insurance_expiry_var.set(self.vehicle_data['insurance_expiry'])
            
        if self.vehicle_data.get('notes'):
            self.notes_text.insert('1.0', self.vehicle_data['notes'])
    
    def validate_fields(self):
        """Valida os campos obrigatórios"""
        if not self.name_var.get().strip():
            messagebox.showerror("Erro", "Nome do veículo é obrigatório!")
            return False
            
        if not self.brand_var.get().strip():
            messagebox.showerror("Erro", "Marca é obrigatória!")
            return False
            
        if not self.model_var.get().strip():
            messagebox.showerror("Erro", "Modelo é obrigatório!")
            return False
            
        try:
            year = int(self.year_var.get())
            if year < 1900 or year > datetime.now().year + 1:
                messagebox.showerror("Erro", "Ano inválido!")
                return False
        except ValueError:
            messagebox.showerror("Erro", "Ano deve ser um número!")
            return False
            
        return True
    
    def save(self):
        """Salva o veículo"""
        if not self.validate_fields():
            return

        try:
            # Validar campos numéricos antes de converter
            mileage_str = self.mileage_var.get().strip()
            if not mileage_str:
                mileage_str = "0"

            purchase_price_str = self.purchase_price_var.get().strip()
            current_value_str = self.current_value_var.get().strip()

            # Preparar dados
            vehicle_data = {
                'name': self.name_var.get().strip(),
                'brand': self.brand_var.get().strip(),
                'model': self.model_var.get().strip(),
                'year': int(self.year_var.get()),
                'license_plate': self.license_plate_var.get().strip() or None,
                'color': self.color_var.get().strip() or None,
                'fuel_type': self.fuel_type_var.get(),
                'engine_size': self.engine_size_var.get().strip() or None,
                'mileage': int(mileage_str),
                'purchase_date': self.purchase_date_var.get() or None,
                'purchase_price': float(purchase_price_str) if purchase_price_str else None,
                'current_value': float(current_value_str) if current_value_str else None,
                'insurance_company': self.insurance_company_var.get().strip() or None,
                'insurance_policy': self.insurance_policy_var.get().strip() or None,
                'insurance_expiry': self.insurance_expiry_var.get() or None,
                'notes': self.notes_text.get('1.0', tk.END).strip() or None
            }

            if self.is_edit_mode:
                # Atualizar veículo existente
                self.db_manager.update_vehicle(self.vehicle_data['id'], vehicle_data)
                messagebox.showinfo("Sucesso", "Veículo atualizado com sucesso!")
            else:
                # Criar novo veículo
                self.db_manager.add_vehicle(self.user_id, vehicle_data)
                messagebox.showinfo("Sucesso", "Veículo cadastrado com sucesso!")

            # Chamar callback se fornecido
            if self.callback:
                self.callback()

            # Fechar janela
            self.window.destroy()

        except ValueError as e:
            messagebox.showerror("Erro", f"Dados inválidos: {str(e)}")
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao salvar veículo: {str(e)}")
    
    def cancel(self):
        """Cancela a operação"""
        self.window.destroy()
