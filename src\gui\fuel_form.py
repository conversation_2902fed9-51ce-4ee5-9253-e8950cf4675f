#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formulário para gerenciamento de registros de combustível
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from tkcalendar import DateEntry

class FuelForm:
    def __init__(self, parent, db_manager, user_id, vehicle_id=None, fuel_data=None, callback=None):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.vehicle_id = vehicle_id
        self.fuel_data = fuel_data
        self.callback = callback
        self.is_edit_mode = fuel_data is not None
        
        # Criar janela
        self.window = tk.Toplevel(parent)
        self.window.title("Editar Abastecimento" if self.is_edit_mode else "Novo Abastecimento")
        self.window.geometry("500x650")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        # Centralizar janela
        self.center_window()
        
        # Carregar veículos
        self.load_vehicles()
        
        # Criar interface
        self.create_widgets()
        
        # Preencher dados se for edição
        if self.is_edit_mode:
            self.populate_fields()
        
        # Configurar eventos
        self.setup_events()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"500x650+{x}+{y}")
    
    def load_vehicles(self):
        """Carrega lista de veículos do usuário"""
        try:
            self.vehicles = self.db_manager.get_user_vehicles(self.user_id)
            self.vehicle_options = {f"{v['name']} - {v['brand']} {v['model']}": v['id'] for v in self.vehicles}
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar veículos: {str(e)}")
            self.vehicles = []
            self.vehicle_options = {}
    
    def create_widgets(self):
        """Cria os widgets do formulário"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title = ttk.Label(main_frame, text="Editar Abastecimento" if self.is_edit_mode else "Novo Abastecimento",
                         font=('Arial', 14, 'bold'))
        title.pack(pady=(0, 20))
        
        # Frame para campos
        fields_frame = ttk.Frame(main_frame)
        fields_frame.pack(fill=tk.BOTH, expand=True)
        
        # Veículo
        ttk.Label(fields_frame, text="Veículo:*").grid(row=0, column=0, sticky='w', pady=5)
        self.vehicle_var = tk.StringVar()
        vehicle_combo = ttk.Combobox(fields_frame, textvariable=self.vehicle_var, width=37, state='readonly')
        vehicle_combo['values'] = list(self.vehicle_options.keys())
        vehicle_combo.grid(row=0, column=1, sticky='ew', pady=5)
        
        # Pré-selecionar veículo se fornecido
        if self.vehicle_id:
            for name, vid in self.vehicle_options.items():
                if vid == self.vehicle_id:
                    self.vehicle_var.set(name)
                    break
        
        # Data do abastecimento
        ttk.Label(fields_frame, text="Data:*").grid(row=1, column=0, sticky='w', pady=5)
        self.fuel_date_var = tk.StringVar()
        try:
            self.fuel_date_entry = DateEntry(fields_frame, textvariable=self.fuel_date_var,
                                           width=37, background='darkblue',
                                           foreground='white', borderwidth=2, date_pattern='dd/mm/yyyy')
            self.fuel_date_entry.grid(row=1, column=1, sticky='ew', pady=5)
            # Definir data atual como padrão
            self.fuel_date_entry.set_date(date.today())
        except:
            ttk.Entry(fields_frame, textvariable=self.fuel_date_var, width=40).grid(row=1, column=1, sticky='ew', pady=5)
        
        # Tipo de combustível
        ttk.Label(fields_frame, text="Combustível:*").grid(row=2, column=0, sticky='w', pady=5)
        self.fuel_type_var = tk.StringVar(value='gasoline')
        fuel_combo = ttk.Combobox(fields_frame, textvariable=self.fuel_type_var, width=37, state='readonly')
        fuel_combo['values'] = ('gasoline', 'ethanol', 'diesel')
        fuel_combo.grid(row=2, column=1, sticky='ew', pady=5)
        
        # Litros
        ttk.Label(fields_frame, text="Litros:*").grid(row=3, column=0, sticky='w', pady=5)
        self.liters_var = tk.StringVar()
        self.liters_entry = ttk.Entry(fields_frame, textvariable=self.liters_var, width=40)
        self.liters_entry.grid(row=3, column=1, sticky='ew', pady=5)
        
        # Preço por litro
        ttk.Label(fields_frame, text="Preço/Litro:*").grid(row=4, column=0, sticky='w', pady=5)
        self.price_per_liter_var = tk.StringVar()
        self.price_per_liter_entry = ttk.Entry(fields_frame, textvariable=self.price_per_liter_var, width=40)
        self.price_per_liter_entry.grid(row=4, column=1, sticky='ew', pady=5)
        
        # Custo total
        ttk.Label(fields_frame, text="Total:*").grid(row=5, column=0, sticky='w', pady=5)
        self.total_cost_var = tk.StringVar()
        self.total_cost_entry = ttk.Entry(fields_frame, textvariable=self.total_cost_var, width=40)
        self.total_cost_entry.grid(row=5, column=1, sticky='ew', pady=5)
        
        # Quilometragem
        ttk.Label(fields_frame, text="Quilometragem:").grid(row=6, column=0, sticky='w', pady=5)
        self.mileage_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.mileage_var, width=40).grid(row=6, column=1, sticky='ew', pady=5)
        
        # Posto de gasolina
        ttk.Label(fields_frame, text="Posto:").grid(row=7, column=0, sticky='w', pady=5)
        self.gas_station_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.gas_station_var, width=40).grid(row=7, column=1, sticky='ew', pady=5)
        
        # Localização
        ttk.Label(fields_frame, text="Localização:").grid(row=8, column=0, sticky='w', pady=5)
        self.location_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.location_var, width=40).grid(row=8, column=1, sticky='ew', pady=5)
        
        # Número do recibo
        ttk.Label(fields_frame, text="Nº Recibo:").grid(row=9, column=0, sticky='w', pady=5)
        self.receipt_number_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.receipt_number_var, width=40).grid(row=9, column=1, sticky='ew', pady=5)
        
        # Checkbox tanque cheio
        checkbox_frame = ttk.Frame(fields_frame)
        checkbox_frame.grid(row=10, column=1, sticky='w', pady=10)
        
        self.is_full_tank_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(checkbox_frame, text="Tanque Cheio", 
                       variable=self.is_full_tank_var).pack(anchor='w')
        
        # Eficiência (calculada automaticamente)
        ttk.Label(fields_frame, text="Eficiência (km/l):").grid(row=11, column=0, sticky='w', pady=5)
        self.efficiency_var = tk.StringVar()
        efficiency_entry = ttk.Entry(fields_frame, textvariable=self.efficiency_var, width=40, state='readonly')
        efficiency_entry.grid(row=11, column=1, sticky='ew', pady=5)
        
        # Observações
        ttk.Label(fields_frame, text="Observações:").grid(row=12, column=0, sticky='nw', pady=5)
        self.notes_text = tk.Text(fields_frame, height=4, width=40)
        self.notes_text.grid(row=12, column=1, sticky='ew', pady=5)
        
        # Configurar grid
        fields_frame.columnconfigure(1, weight=1)
        
        # Frame para botões
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Botões
        ttk.Button(buttons_frame, text="Cancelar", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Salvar", command=self.save).pack(side=tk.RIGHT)
    
    def setup_events(self):
        """Configura eventos para cálculo automático"""
        # Calcular total quando litros ou preço mudarem
        self.liters_var.trace('w', self.calculate_total)
        self.price_per_liter_var.trace('w', self.calculate_total)
    
    def calculate_total(self, *args):
        """Calcula o total automaticamente"""
        try:
            liters = float(self.liters_var.get() or 0)
            price_per_liter = float(self.price_per_liter_var.get() or 0)
            total = liters * price_per_liter
            self.total_cost_var.set(f"{total:.2f}")
        except ValueError:
            pass
    
    def populate_fields(self):
        """Preenche os campos com dados do abastecimento para edição"""
        if not self.fuel_data:
            return
        
        # Selecionar veículo
        for name, vid in self.vehicle_options.items():
            if vid == self.fuel_data['vehicle_id']:
                self.vehicle_var.set(name)
                break

        if self.fuel_data['fuel_date']:
            self.fuel_date_var.set(self.fuel_data['fuel_date'])

        self.fuel_type_var.set(self.fuel_data['fuel_type'] or 'gasoline')

        if self.fuel_data['liters']:
            self.liters_var.set(str(self.fuel_data['liters']))

        if self.fuel_data['price_per_liter']:
            self.price_per_liter_var.set(str(self.fuel_data['price_per_liter']))

        if self.fuel_data['total_cost']:
            self.total_cost_var.set(str(self.fuel_data['total_cost']))

        if self.fuel_data['mileage']:
            self.mileage_var.set(str(self.fuel_data['mileage']))

        self.gas_station_var.set(self.fuel_data['gas_station'] or '')
        self.location_var.set(self.fuel_data['location'] or '')
        self.receipt_number_var.set(self.fuel_data['receipt_number'] or '')

        self.is_full_tank_var.set(self.fuel_data['is_full_tank'] if self.fuel_data['is_full_tank'] is not None else True)

        if self.fuel_data['fuel_efficiency']:
            self.efficiency_var.set(f"{self.fuel_data['fuel_efficiency']:.2f}")

        if self.fuel_data['notes']:
            self.notes_text.insert('1.0', self.fuel_data['notes'])
    
    def validate_fields(self):
        """Valida os campos obrigatórios"""
        if not self.vehicle_var.get():
            messagebox.showerror("Erro", "Selecione um veículo!")
            return False
        
        if not self.fuel_date_var.get():
            messagebox.showerror("Erro", "Data é obrigatória!")
            return False
        
        if not self.fuel_type_var.get():
            messagebox.showerror("Erro", "Tipo de combustível é obrigatório!")
            return False
        
        if not self.liters_var.get():
            messagebox.showerror("Erro", "Quantidade de litros é obrigatória!")
            return False
        
        if not self.price_per_liter_var.get():
            messagebox.showerror("Erro", "Preço por litro é obrigatório!")
            return False
        
        if not self.total_cost_var.get():
            messagebox.showerror("Erro", "Custo total é obrigatório!")
            return False
        
        try:
            float(self.liters_var.get())
            float(self.price_per_liter_var.get())
            float(self.total_cost_var.get())
        except ValueError:
            messagebox.showerror("Erro", "Valores numéricos inválidos!")
            return False
        
        return True
    
    def save(self):
        """Salva o registro de combustível"""
        if not self.validate_fields():
            return
        
        try:
            # Obter ID do veículo selecionado
            selected_vehicle = self.vehicle_var.get()
            vehicle_id = self.vehicle_options.get(selected_vehicle)
            
            if not vehicle_id:
                messagebox.showerror("Erro", "Veículo selecionado inválido!")
                return
            
            # Converter data do formato brasileiro para ISO se necessário
            fuel_date = self.fuel_date_var.get()
            if fuel_date:
                try:
                    # Tentar converter de dd/mm/yyyy para yyyy-mm-dd
                    if '/' in fuel_date:
                        day, month, year = fuel_date.split('/')
                        fuel_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                except:
                    pass  # Manter formato original se conversão falhar

            # Preparar dados
            fuel_data = {
                'vehicle_id': vehicle_id,
                'fuel_date': fuel_date,
                'fuel_type': self.fuel_type_var.get(),
                'liters': float(self.liters_var.get()),
                'price_per_liter': float(self.price_per_liter_var.get()),
                'total_cost': float(self.total_cost_var.get()),
                'mileage': int(self.mileage_var.get()) if self.mileage_var.get() else None,
                'gas_station': self.gas_station_var.get().strip() or None,
                'location': self.location_var.get().strip() or None,
                'receipt_number': self.receipt_number_var.get().strip() or None,
                'is_full_tank': self.is_full_tank_var.get(),
                'notes': self.notes_text.get('1.0', tk.END).strip() or None
            }
            
            if self.is_edit_mode:
                # Atualizar registro existente
                self.db_manager.update_fuel_record(self.fuel_data['id'], fuel_data)
                messagebox.showinfo("Sucesso", "Abastecimento atualizado com sucesso!")
            else:
                # Criar novo registro
                self.db_manager.add_fuel_record(self.user_id, fuel_data)
                messagebox.showinfo("Sucesso", "Abastecimento registrado com sucesso!")
            
            # Chamar callback se fornecido
            if self.callback:
                self.callback()
            
            # Fechar janela
            self.window.destroy()
            
        except ValueError as e:
            messagebox.showerror("Erro", f"Dados inválidos: {str(e)}")
        except Exception as e:
            import traceback
            traceback.print_exc()  # Para debug no console
            messagebox.showerror("Erro", f"Erro ao salvar abastecimento: {str(e)}")
    
    def cancel(self):
        """Cancela a operação"""
        self.window.destroy()
