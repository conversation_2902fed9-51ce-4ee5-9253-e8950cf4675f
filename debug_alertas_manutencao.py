#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug dos alertas de manutenção
"""

import sys
import os
from datetime import date, datetime, timedelta

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager

def debug_alertas_detalhado():
    """Debug detalhado dos alertas de manutenção"""
    print("🔍 DEBUG DETALHADO DOS ALERTAS DE MANUTENÇÃO")
    print("=" * 60)
    
    try:
        # Inicializar sistema
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        user_id = user_data['id']
        today = date.today()
        
        # 1. Verificar estrutura da tabela vehicle_maintenance
        print(f"\n📋 VERIFICANDO ESTRUTURA DA TABELA...")
        try:
            schema_query = "PRAGMA table_info(vehicle_maintenance)"
            columns = db_manager.execute_query(schema_query)
            print(f"   📊 Colunas da tabela vehicle_maintenance:")
            for col in columns:
                print(f"      • {col['name']}: {col['type']} (NOT NULL: {col['notnull']})")
        except Exception as e:
            print(f"   ❌ Erro ao verificar estrutura: {str(e)}")
        
        # 2. Verificar todos os registros de manutenção
        print(f"\n🔧 VERIFICANDO TODOS OS REGISTROS DE MANUTENÇÃO...")
        all_maintenance = db_manager.execute_query("""
            SELECT vm.*, v.name as vehicle_name, v.brand, v.model, v.mileage as current_mileage
            FROM vehicle_maintenance vm
            JOIN vehicles v ON vm.vehicle_id = v.id
            WHERE vm.user_id = ?
            ORDER BY vm.service_date DESC
        """, (user_id,))
        
        print(f"   📊 Total de registros: {len(all_maintenance)}")
        
        for i, maint in enumerate(all_maintenance, 1):
            print(f"\n   {i:2d}. ID {maint['id']}: {maint['maintenance_type']}")
            print(f"       Veículo: {maint['vehicle_name']} ({maint['brand']} {maint['model']})")
            print(f"       Descrição: {maint['description']}")
            print(f"       Data serviço: {maint['service_date']}")
            print(f"       Custo: R$ {maint['cost']:.2f}")
            print(f"       Próxima data: {maint.get('next_service_date', 'N/A')}")
            print(f"       Próxima KM: {maint.get('next_service_mileage', 'N/A')}")
            print(f"       KM atual veículo: {maint['current_mileage']}")
            print(f"       Concluída: {maint.get('is_completed', 'N/A')}")
            print(f"       Agendada: {maint.get('is_scheduled', 'N/A')}")
            
            # Verificar se deveria gerar alerta
            if maint.get('next_service_date'):
                next_date = datetime.strptime(maint['next_service_date'], '%Y-%m-%d').date()
                days_until = (next_date - today).days
                print(f"       ⏰ Dias até próxima: {days_until}")
                
                if days_until <= 30 and maint.get('is_completed') == 1 and maint.get('is_scheduled') == 0:
                    print(f"       ✅ DEVERIA GERAR ALERTA POR DATA!")
                else:
                    print(f"       ❌ Não gera alerta por data (completed={maint.get('is_completed')}, scheduled={maint.get('is_scheduled')})")
            
            if maint.get('next_service_mileage') and maint['current_mileage']:
                km_remaining = maint['next_service_mileage'] - maint['current_mileage']
                print(f"       🛣️  KM restantes: {km_remaining}")
                
                if km_remaining <= 1000 and maint.get('is_completed') == 1 and maint.get('is_scheduled') == 0:
                    print(f"       ✅ DEVERIA GERAR ALERTA POR KM!")
                else:
                    print(f"       ❌ Não gera alerta por KM (completed={maint.get('is_completed')}, scheduled={maint.get('is_scheduled')})")
        
        # 3. Testar query de alertas por data manualmente
        print(f"\n📅 TESTANDO QUERY DE ALERTAS POR DATA...")
        alert_date = today + timedelta(days=30)
        
        date_query = '''
            SELECT
                vm.id,
                vm.vehicle_id,
                vm.maintenance_type,
                vm.description,
                vm.next_service_date,
                vm.next_service_mileage,
                v.name as vehicle_name,
                v.brand,
                v.model,
                v.mileage as current_mileage,
                'date' as alert_type,
                (julianday(vm.next_service_date) - julianday('now')) as days_until,
                vm.is_completed,
                vm.is_scheduled,
                v.is_active
            FROM vehicle_maintenance vm
            JOIN vehicles v ON vm.vehicle_id = v.id
            WHERE vm.user_id = ?
            AND vm.next_service_date IS NOT NULL
            AND vm.next_service_date <= ?
            AND vm.is_completed = 1
            AND vm.is_scheduled = 0
            AND v.is_active = 1
        '''
        
        print(f"   🔍 Parâmetros da query:")
        print(f"      user_id: {user_id}")
        print(f"      alert_date: {alert_date.isoformat()}")
        
        date_alerts = db_manager.execute_query(date_query, (user_id, alert_date.isoformat()))
        print(f"   📊 Alertas por data encontrados: {len(date_alerts)}")
        
        for alert in date_alerts:
            print(f"      🔧 {alert['vehicle_name']} - {alert['maintenance_type']}")
            print(f"         Próxima data: {alert['next_service_date']}")
            print(f"         Dias até: {alert['days_until']:.1f}")
            print(f"         Completed: {alert['is_completed']}, Scheduled: {alert['is_scheduled']}, Active: {alert['is_active']}")
        
        # 4. Testar query de alertas por quilometragem manualmente
        print(f"\n🛣️  TESTANDO QUERY DE ALERTAS POR QUILOMETRAGEM...")
        
        mileage_query = '''
            SELECT
                vm.id,
                vm.vehicle_id,
                vm.maintenance_type,
                vm.description,
                vm.next_service_date,
                vm.next_service_mileage,
                v.name as vehicle_name,
                v.brand,
                v.model,
                v.mileage as current_mileage,
                'mileage' as alert_type,
                (vm.next_service_mileage - v.mileage) as km_remaining,
                vm.is_completed,
                vm.is_scheduled,
                v.is_active
            FROM vehicle_maintenance vm
            JOIN vehicles v ON vm.vehicle_id = v.id
            WHERE vm.user_id = ?
            AND vm.next_service_mileage IS NOT NULL
            AND v.mileage >= (vm.next_service_mileage - 1000)
            AND vm.is_completed = 1
            AND vm.is_scheduled = 0
            AND v.is_active = 1
        '''
        
        mileage_alerts = db_manager.execute_query(mileage_query, (user_id,))
        print(f"   📊 Alertas por quilometragem encontrados: {len(mileage_alerts)}")
        
        for alert in mileage_alerts:
            print(f"      🔧 {alert['vehicle_name']} - {alert['maintenance_type']}")
            print(f"         Próxima KM: {alert['next_service_mileage']}")
            print(f"         KM atual: {alert['current_mileage']}")
            print(f"         KM restantes: {alert['km_remaining']}")
            print(f"         Completed: {alert['is_completed']}, Scheduled: {alert['is_scheduled']}, Active: {alert['is_active']}")
        
        # 5. Testar query de manutenções vencidas manualmente
        print(f"\n🚨 TESTANDO QUERY DE MANUTENÇÕES VENCIDAS...")
        
        overdue_query = '''
            SELECT
                vm.*,
                v.name as vehicle_name,
                v.brand,
                v.model,
                v.mileage as current_mileage,
                (julianday('now') - julianday(vm.next_service_date)) as days_overdue,
                vm.is_completed,
                vm.is_scheduled,
                v.is_active
            FROM vehicle_maintenance vm
            JOIN vehicles v ON vm.vehicle_id = v.id
            WHERE vm.user_id = ?
            AND vm.next_service_date IS NOT NULL
            AND vm.next_service_date < ?
            AND vm.is_completed = 1
            AND vm.is_scheduled = 0
            AND v.is_active = 1
            ORDER BY vm.next_service_date ASC
        '''
        
        overdue_alerts = db_manager.execute_query(overdue_query, (user_id, today.isoformat()))
        print(f"   📊 Manutenções vencidas encontradas: {len(overdue_alerts)}")
        
        for alert in overdue_alerts:
            print(f"      🔴 {alert['vehicle_name']} - {alert['maintenance_type']}")
            print(f"         Data prevista: {alert['next_service_date']}")
            print(f"         Dias em atraso: {alert['days_overdue']:.1f}")
            print(f"         Completed: {alert['is_completed']}, Scheduled: {alert['is_scheduled']}, Active: {alert['is_active']}")
        
        # 6. Testar função get_maintenance_alerts
        print(f"\n🔧 TESTANDO FUNÇÃO get_maintenance_alerts...")
        function_alerts = db_manager.get_maintenance_alerts(user_id, 30)
        print(f"   📊 Alertas da função: {len(function_alerts)}")
        
        for alert in function_alerts:
            print(f"      🔧 {alert['vehicle_name']} - {alert['maintenance_type']}")
            print(f"         Tipo: {alert['alert_type']}")
            if alert['alert_type'] == 'date':
                print(f"         Dias até: {alert['days_until']:.1f}")
            else:
                print(f"         KM restantes: {alert['km_remaining']}")
        
        # 7. Testar função get_overdue_maintenance
        print(f"\n🚨 TESTANDO FUNÇÃO get_overdue_maintenance...")
        function_overdue = db_manager.get_overdue_maintenance(user_id)
        print(f"   📊 Vencidas da função: {len(function_overdue)}")
        
        for alert in function_overdue:
            print(f"      🔴 {alert['vehicle_name']} - {alert['maintenance_type']}")
            print(f"         Dias em atraso: {alert['days_overdue']:.1f}")
        
        # 8. Resumo e diagnóstico
        print(f"\n📋 RESUMO DO DIAGNÓSTICO:")
        print(f"   📊 Total de manutenções: {len(all_maintenance)}")
        print(f"   📅 Alertas por data (query manual): {len(date_alerts)}")
        print(f"   🛣️  Alertas por KM (query manual): {len(mileage_alerts)}")
        print(f"   🚨 Vencidas (query manual): {len(overdue_alerts)}")
        print(f"   🔧 Alertas (função): {len(function_alerts)}")
        print(f"   🚨 Vencidas (função): {len(function_overdue)}")
        
        # Verificar problemas comuns
        print(f"\n🔍 VERIFICANDO PROBLEMAS COMUNS:")
        
        # Verificar se há manutenções com is_completed = 0
        incomplete = [m for m in all_maintenance if m.get('is_completed') != 1]
        if incomplete:
            print(f"   ⚠️  {len(incomplete)} manutenções com is_completed != 1")
        
        # Verificar se há manutenções com is_scheduled = 1
        scheduled = [m for m in all_maintenance if m.get('is_scheduled') == 1]
        if scheduled:
            print(f"   ⚠️  {len(scheduled)} manutenções com is_scheduled = 1")
        
        # Verificar se há veículos inativos
        inactive_vehicles = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM vehicles WHERE user_id = ? AND is_active != 1
        """, (user_id,))
        if inactive_vehicles and inactive_vehicles[0]['count'] > 0:
            print(f"   ⚠️  {inactive_vehicles[0]['count']} veículos inativos")
        
        # Verificar se há manutenções sem next_service_date ou next_service_mileage
        no_next_service = [m for m in all_maintenance if not m.get('next_service_date') and not m.get('next_service_mileage')]
        if no_next_service:
            print(f"   ⚠️  {len(no_next_service)} manutenções sem próxima data/KM definida")
        
        print(f"\n✅ DEBUG CONCLUÍDO!")
        
    except Exception as e:
        print(f"❌ ERRO: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_alertas_detalhado()
